{% extends "base.html" %}

{% block title %}全部检验 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 主容器样式 */
    .container {
        width: 100%;
        padding: 0;
        font-size: 12px;
        max-width: none;
        margin: 0;
    }
    
    .modal {
        display: none;
        position: fixed;
        z-index: 1050;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
    }
    
    .modal-content {
        background-color: white;
        margin: 7% auto;
        padding: 8px;
        border-radius: 3px;
        width: 80%;
        max-width: 500px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    .close {
        color: #aaa;
        float: right;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .record-details p {
        margin: 2px 0;
        font-size: 11px;
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }
    
    .pagination a, .pagination span {
        padding: 2px 5px;
        text-decoration: none;
        border: 1px solid #ddd;
        margin: 0 2px;
        color: #333;
        font-size: 11px;
    }
    
    .pagination a:hover {
        background-color: #f1f1f1;
    }
    
    .pagination .active {
        background-color: #1976d2;
        color: white;
        border: 1px solid #1976d2;
    }
    
    .pagination .disabled {
        color: #aaa;
        border: 1px solid #ddd;
    }
    
    .summary {
        margin-bottom: 5px;
        color: #666;
        font-size: 0.75em;
    }
    
    /* 分页容器样式 */
    .pagination-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: 2px;
        padding: 2px 0;
    }
    
    .page-size-selector {
        font-size: 11px;
        color: #666;
        display: flex;
        align-items: center;
        white-space: nowrap;
        min-width: 130px;
    }
    
    .page-size-selector select {
        padding: 2px 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 11px;
        margin-left: 5px;
        cursor: pointer;
        width: 65px;
    }
    
    .pagination-controls {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }
    
    /* 表格紧凑样式 */
    .sortable-table {
        font-size: 11px;
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
        border-spacing: 0;
        margin: 0;
    }
    
    .sortable-table th, .sortable-table td {
        padding: 3px 6px;
        white-space: nowrap;
        border: 1px solid #e0e0e0;
        text-align: center;
        vertical-align: middle;
        overflow: hidden;
        text-overflow: ellipsis;
        box-sizing: border-box;
    }
    
    /* 设置各列的宽度 */
    .sortable-table th[data-sort="id"],
    .sortable-table td[data-column="id"] {
        width: 4%;
    }
    
    .sortable-table th[data-sort="material_number"],
    .sortable-table td[data-column="material_number"] {
        width: 8%;
    }
    
    .sortable-table th[data-sort="material_name"],
    .sortable-table td[data-column="material_name"] {
        width: 10%;
    }
    
    .sortable-table th[data-sort="specification"],
    .sortable-table td[data-column="specification"] {
        width: 8%;
    }
    
    .sortable-table th[data-sort="supplier"],
    .sortable-table td[data-column="supplier"] {
        width: 10%;
    }
    
    .sortable-table th[data-sort="purchase_order"],
    .sortable-table td[data-column="purchase_order"] {
        width: 8%;
    }
    
    .sortable-table th[data-sort="receipt_date"],
    .sortable-table td[data-column="receipt_date"],
    .sortable-table th[data-sort="inspection_date"],
    .sortable-table td[data-column="inspection_date"] {
        width: 7%;
    }
    
    .sortable-table th[data-sort="total_quantity"],
    .sortable-table td[data-column="total_quantity"],
    .sortable-table th[data-sort="qualified_quantity"],
    .sortable-table td[data-column="qualified_quantity"],
    .sortable-table th[data-sort="defect_quantity"],
    .sortable-table td[data-column="defect_quantity"],
    .sortable-table th[data-sort="qualified_rate"],
    .sortable-table td[data-column="qualified_rate"] {
        width: 6%;
    }
    
    .sortable-table td[data-column="defect_issues"] {
        white-space: normal;
        width: 15%;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .sortable-table th:last-child,
    .sortable-table td:last-child {
        width: 5%;
    }
    
    /* 按钮样式统一 */
    .btn {
        height: 32px; /* 减小高度 */
        line-height: 30px; /* 减小行高 */
        padding: 0 12px; /* 减小左右内边距 */
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 12px; /* 减小字体大小 */
        transition: all 0.2s;
        vertical-align: middle;
        box-sizing: border-box;
    }
    
    .btn-secondary, .btn-success, .btn-primary {
        min-width: 80px; /* 减小最小宽度 */
    }
    
    /* 取消新增记录按钮的下划线 */
    .btn-success {
        text-decoration: none;
    }
    
    /* 页面标题和操作按钮区域 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .header-left {
        display: flex;
        align-items: center;
    }
    
    .header-right {
        display: flex;
        gap: 8px;
    }
    
    /* 表头样式 */
    .sortable-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        font-size: 11px;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    /* 表格容器 */
    .table-container {
        margin: 0;
        padding: 0;
        width: 100%;
        box-sizing: border-box;
        height: calc(100vh - 120px);
        min-height: 500px;
        overflow-x: auto;
    }
    
    /* 紧凑表格行 */
    .sortable-table tr {
        height: 24px;
        transition: background-color 0.2s;
    }

    .sortable-table tbody tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .sortable-table tbody tr:hover {
        background-color: #eaf2fd;
    }
    
    /* 搜索表单样式 */
    .search-form {
        background-color: #f8f9fa;
        padding: 6px;
        border-radius: 3px;
        margin-bottom: 8px;
        border: 1px solid #e9ecef;
    }
    
    /* 表单元素样式 */
    .form-group {
        margin-bottom: 5px;
        display: flex;
        align-items: center;
    }
    
    .form-label {
        width: 70px;
        text-align: right;
        padding-right: 8px;
        flex-shrink: 0;
        font-size: 11px;
        font-weight: 500;
        color: #555;
    }
    
    .form-input {
        flex: 1;
    }
    
    /* 表单元素样式统一 */
    input[type="text"], input[type="date"], input[type="number"], select, .quick-search-input {
        height: 32px;
        padding: 2px 8px;
        font-size: 12px;
        width: 100%;
        max-width: 180px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    
    /* 日期组样式 */
    .date-group {
        margin-bottom: 10px;
    }
    
    .date-label {
        font-size: 11px;
        font-weight: 500;
        color: #1976d2;
        margin-bottom: 4px;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 2px;
    }
    
    .date-inputs {
        display: flex;
        gap: 10px;
    }
    
    .date-inputs .form-group {
        margin-bottom: 0;
    }
    
    .date-inputs .form-label {
        width: 40px;
    }
    
    /* 范围输入样式 */
    .range-input {
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .operator-select {
        width: 80px;
        flex-shrink: 0;
    }
    
    .unit-label {
        font-size: 11px;
        color: #555;
        margin-left: 2px;
    }
    
    /* 两列布局 */
    .two-column-layout {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -8px;
    }
    
    .column {
        padding: 0 8px;
        box-sizing: border-box;
    }
    
    .left-column {
        flex: 0 0 40%;
    }
    
    .right-column {
        flex: 0 0 60%;
    }
    
    .search-buttons {
        text-align: right;
        margin-top: 5px;
    }
    
    /* 快速搜索表单 */
    .quick-search-form {
        display: flex;
        align-items: center;
        margin-right: 10px;
        position: relative;
        border: 1px solid #1976d2;
        border-radius: 4px;
        overflow: hidden;
        background-color: #fff;
        box-sizing: border-box;
    }
    
    .quick-search-input {
        height: 32px;
        width: 180px;
        padding: 2px 8px;
        padding-right: 30px;
        border: none;
        font-size: 12px;
        outline: none;
        background: transparent;
        z-index: 1;
        max-width: none;
    }
    
    .search-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #1976d2;
        font-size: 14px;
        cursor: pointer;
        z-index: 2;
        pointer-events: auto;
    }
    
    /* 日期范围选择器 */
    .date-range-picker {
        position: relative;
        margin-right: 10px;
    }
    
    .date-range-display {
        height: 32px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        border: 1px solid #1976d2;
        border-radius: 4px;
        background-color: #fff;
        cursor: pointer;
        font-size: 12px;
        min-width: 200px;
        box-sizing: border-box;
    }
    
    .date-range-display i {
        margin: 0 8px;
        color: #1976d2;
    }
    
    .date-type-dropdown {
        margin-right: 5px;
        font-size: 11px;
        color: #333;
        display: inline-flex;
        align-items: center;
    }
    
    .date-type-dropdown select {
        height: 32px;
        padding: 2px 8px;
        font-size: 12px;
        border: 1px solid #1976d2;
        border-radius: 4px;
        cursor: pointer;
        background-color: #fff;
        color: #1976d2;
        outline: none;
        width: 100px;
        box-sizing: border-box;
    }
    
    .date-range-popup {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 9999;
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 10px;
        width: 480px; /* 减小宽度 */
        margin-top: 5px;
        overflow: visible;
        box-sizing: border-box;
        max-height: none;
    }
    
    .date-popup-layout {
        display: flex;
        gap: 8px; /* 进一步减小间距 */
    }

    .quick-ranges-column {
        width: 70px; /* 进一步减小宽度 */
        border-right: 1px solid #eee;
        padding-right: 8px;
        padding-top: 4px; /* 添加顶部内边距 */
    }

    .calendars-column {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .calendar-container {
        display: flex;
        gap: 8px; /* 减小间距 */
        justify-content: space-between;
    }

    .quick-range-btn {
        font-size: 11px;
        padding: 3px 0; /* 减小内边距 */
        margin-bottom: 4px; /* 减小间距 */
        background-color: transparent;
        border: none;
        border-radius: 0;
        cursor: pointer;
        text-align: left;
        transition: color 0.2s;
        color: #666;
        display: block; /* 确保是块级元素 */
        width: 100%; /* 占满整个容器宽度 */
    }

    .quick-range-btn:hover {
        background-color: transparent; /* 移除悬停背景 */
        color: #1976d2; /* 悬停时改变颜色 */
    }

    .date-range-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 10px; /* 减小间距 */
        gap: 8px; /* 减小间距 */
        padding-top: 8px; /* 减小内边距 */
        border-top: 1px solid #f0f0f0;
    }
    
    /* 在非移动设备上保持原有布局 */
    .date-filters-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    /* 日期范围选择器特定样式 */
    @media (min-width: 769px) {
        /* 网页版布局保持不变 */
        .date-type-dropdown {
            margin-right: 0;
        }
    }
    
    /* 移动端响应式样式 */
    @media (max-width: 768px) {
        .two-column-layout {
            flex-direction: column;
        }
        
        .left-column,
        .right-column {
            flex: 0 0 100%;
        }
        
        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 2px;
        }
        
        .page-header {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .header-left {
            margin-bottom: 8px;
        }
        
        .header-right {
            width: 100%;
            flex-wrap: wrap;
        }
        
        .quick-search-form {
            margin-right: 0;
            margin-bottom: 8px;
            width: 100%;
            border: 1px solid #1976d2;
        }
        
        .quick-search-input {
            flex: 1;
            width: auto;
            padding-right: 30px;
        }
        
        /* 其他移动端样式保持不变 */
        
        .quick-search-form {
            width: 100%;
            margin-right: 0;
            margin-bottom: 8px;
            height: 38px; /* 增加高度 */
        }
        
        .quick-search-input {
            width: 100%;
            height: 38px; /* 增加高度 */
            font-size: 13px; /* 增加字体大小 */
        }
        
        .date-range-display {
            width: 100%;
            height: 38px; /* 增加高度与其他元素一致 */
        }
        
        .date-type-dropdown select {
            height: 38px; /* 增加高度与日期选择器一致 */
            font-size: 13px; /* 增加字体大小 */
        }
        
        #advanced-search-btn, 
        .header-right .btn {
            height: 38px; /* 增加高度与其他元素一致 */
            width: 48%;
            margin: 0;
            font-size: 13px; /* 增加字体大小 */
            padding: 0 10px;
        }
        
        /* 移动端特定日期选择器布局 */
        @media (max-width: 600px) {
            /* 日期选择器和类型选择器布局重组 */
            .date-filters-container {
                display: flex;
                width: 100%;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            
            .date-type-dropdown {
                width: 38%;
                margin-bottom: 0;
                margin-right: 2%;
            }
            
            .date-range-picker {
                width: 60%;
                margin-bottom: 0;
            }
            
            /* 搜索表单和按钮布局 */
            .quick-search-form {
                width: 100%;
                margin-bottom: 8px;
            }
        }
    }

    /* 移动端响应式分页样式 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-wrap: nowrap;
            gap: 5px;
        }
        
        .summary {
            order: 1;
            width: auto;
            min-width: 0;
            text-align: left;
            margin-right: 5px;
        }
        
        .pagination {
            order: 2;
            width: auto;
            margin-top: 0;
            flex-grow: 1;
            justify-content: center;
        }
        
        .page-size-selector {
            order: 3;
            width: auto;
            min-width: 0;
            justify-content: flex-end;
        }
        
        .pagination a, .pagination span {
            padding: 3px 6px;
        }
    }

    @media (max-width: 480px) {
        .pagination a, .pagination span {
            padding: 4px 6px;
            min-width: 20px;
            font-size: 12px;
        }
        
        /* 在最小屏幕上调整内容 */
        .summary {
            font-size: 10px;
            min-width: 0;
        }
        
        .page-size-selector {
            font-size: 10px;
            min-width: 0;
        }
        
        .page-size-selector select {
            width: 40px;
            font-size: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div style="width: 100vw; padding: 0; margin: 0; box-sizing: border-box; overflow-x: hidden;">
<div class="page-header" style="width: 100vw; max-width: 100vw; padding: 4px 0; box-sizing: border-box; margin: 0; left: 0; right: 0; position: relative;">
    <div class="header-left" style="padding-left: 10px;">
        <h1 style="margin: 0; padding: 4px 0; font-size: 18px;">全部检验记录</h1>
    </div>
    <div class="header-right" style="padding-right: 10px;">
        <!-- 日期筛选区域 -->
        <div class="date-filters-container">
            <span class="date-type-dropdown">
                <select id="date-type-selector">
                    <option value="inspection_date" selected>检验日期</option>
                    <option value="receipt_date">来料日期</option>
                </select>
            </span>
            <div class="date-range-picker">
                <div class="date-range-display" id="date-range-display">
                    <i class="fas fa-calendar-alt"></i>
                    <span id="selected-date-range">{{ start_date }} ~ {{ end_date }}</span>
                </div>
                <div class="date-range-popup" id="date-range-popup">
                    <div class="date-popup-layout">
                        <div class="quick-ranges-column">
                            <div class="quick-range-btn" data-days="0">今天</div>
                            <div class="quick-range-btn" data-days="1">昨天</div>
                            <div class="quick-range-btn" data-days="7">近7天</div>
                            <div class="quick-range-btn" data-days="30">近30天</div>
                            <div class="quick-range-btn" data-days="60">近60天</div>
                            <div class="quick-range-btn" data-days="90">近90天</div>
                            <div class="quick-range-btn" data-days="180">近6个月</div>
                            <div class="quick-range-btn" data-days="365">今年</div>
                        </div>
                        <div class="calendars-column">
                            <div class="calendar-container">
                                <!-- 日历将由JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="start-date-input">
                    <input type="hidden" id="end-date-input">
                    <div class="date-range-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-date-range">取消</button>
                        <button type="button" class="btn btn-primary" id="confirm-date-range">确认</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 搜索框 -->
        <div class="quick-search-form">
            <input type="text" id="quick-search-input" class="quick-search-input" placeholder="请输入料号/名称/供应商/采购单号/检验结果进行搜索..." value="{{ quick_search or '' }}">
            <i class="fas fa-search search-icon" id="search-icon"></i>
        </div>
        
        <!-- 按钮区域 -->
        <div class="buttons-container">
            <button id="advanced-search-btn" class="btn btn-secondary">高级搜索</button>
            <a href="{{ url_for('full_inspection.new_inspection') }}" class="btn btn-success">新增记录</a>
            <a href="{{ url_for('batch_import.full_pending') }}" class="btn btn-primary" title="批量导入待检物料">
                <i class="fas fa-list-ul"></i>
                批量导入待检
            </a>
        </div>
    </div>
</div>

<!-- 表格容器 -->
<div class="table-container" style="width: 100vw; max-width: 100vw; padding: 0; box-sizing: border-box; overflow-x: auto; margin: 0; left: 0; right: 0; position: relative; height: calc(100vh - 120px); min-height: 500px;">
    <table class="sortable-table" style="width: 100%; table-layout: fixed; margin: 0; border-spacing: 0;">
        <thead>
            <tr>
                <th data-sort="id" style="width: 4%;">序号</th>
                <th data-sort="report_code" style="width: 12%;">报告编码</th>
                <th data-sort="material_number" style="width: 8%;">料号</th>
                <th data-sort="material_name" style="width: 10%;">名称</th>
                <th data-sort="specification" style="width: 8%;">规格</th>
                <th data-sort="supplier" style="width: 10%;">供应商</th>
                <th data-sort="purchase_order" style="width: 8%;">采购单号</th>
                <th data-sort="receipt_date" style="width: 7%;">来料日期</th>
                <th data-sort="inspection_date" style="width: 7%;">检验日期</th>
                <th data-sort="total_quantity" style="width: 6%;">来料数量</th>
                <th data-sort="qualified_quantity" style="width: 6%;">合格数量</th>
                <th data-sort="defect_quantity" style="width: 6%;">不良数量</th>
                <th data-sort="defect_issues" style="width: 15%;">问题点</th>
                <th data-sort="qualified_rate" style="width: 6%;">合格率</th>
                <th style="width: 5%;">操作</th>
            </tr>
        </thead>
        <tbody>
            {% for record in records %}
            <tr>
                <td data-column="id">{{ (page - 1) * per_page + loop.index }}</td>
                <td data-column="report_code" title="{{ record.report_code or '-' }}">{{ record.report_code or '-' }}</td>
                <td data-column="material_number" title="{{ record.material_number }}">{{ record.material_number }}</td>
                <td data-column="material_name" title="{{ record.material_name }}">{{ record.material_name }}</td>
                <td data-column="specification" title="{{ record.specification }}">{{ record.specification }}</td>
                <td data-column="supplier" title="{{ record.supplier }}">{{ record.supplier }}</td>
                <td data-column="purchase_order" title="{{ record.purchase_order }}">{{ record.purchase_order }}</td>
                <td data-column="receipt_date">{{ record.receipt_date }}</td>
                <td data-column="inspection_date">{{ record.inspection_date }}</td>
                <td data-column="total_quantity">{{ record.total_quantity }}</td>
                <td data-column="qualified_quantity">{{ record.qualified_quantity }}</td>
                <td data-column="defect_quantity">{{ record.defect_quantity }}</td>
                <td data-column="defect_issues" title="{{ record.defect_issues }}">{{ record.defect_issues|truncate(10, true) if record.defect_issues else '-' }}</td>
                <td data-column="qualified_rate">{{ "{:.2f}%".format((record.qualified_quantity / record.total_quantity * 100) if record.total_quantity > 0 else 0) }}</td>
                <td>
                    <button class="btn btn-primary" onclick="viewInspectionDetails('{{ record.id }}')">详情</button>
                </td>
            </tr>
            {% else %}
            <tr>
                <td colspan="15" style="text-align: center;">暂无全部检验记录</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- 分页和记录统计信息 -->
<div class="pagination-container" style="display: flex; align-items: center; justify-content: space-between; margin-top: 2px; padding: 2px 10px; flex-wrap: nowrap; width: 100vw; max-width: 100vw; box-sizing: border-box; left: 0; right: 0; position: relative;">
    <div class="summary" style="color: #666; white-space: nowrap; font-size: 11px; min-width: auto; padding: 0; margin: 0;">
        显示 {{ records|length }}/{{ total_records }}
    </div>

    <div class="pagination" style="display: flex; align-items: center; margin: 0; padding: 0; flex-grow: 1; justify-content: center; height: 24px;">
        {% if page > 1 %}
        <a href="{{ url_for('full_inspection.index', page=1, per_page=per_page, start_date=start_date, end_date=end_date, date_type=date_type, material_number=material_number, supplier=supplier, quick_search=quick_search) }}" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f1f1f1; border: 1px solid #ddd;">首页</a>
        <a href="{{ url_for('full_inspection.index', page=page-1, per_page=per_page, start_date=start_date, end_date=end_date, date_type=date_type, material_number=material_number, supplier=supplier, quick_search=quick_search) }}" class="page-nav-btn prev-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f1f1f1; border: 1px solid #ddd;">上一页</a>
        {% else %}
        <span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">首页</span>
        <span class="disabled page-nav-btn prev-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">上一页</span>
        {% endif %}
        
        <span class="current-page-display" style="display: inline-block; margin: 0 1px; padding: 1px 8px; background-color: #1976d2; border: 1px solid #1976d2; color: white; min-width: 15px; text-align: center;">{{ page }}</span>
        
        {% if page < total_pages %}
        <a href="{{ url_for('full_inspection.index', page=page+1, per_page=per_page, start_date=start_date, end_date=end_date, date_type=date_type, material_number=material_number, supplier=supplier, quick_search=quick_search) }}" class="page-nav-btn next-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f1f1f1; border: 1px solid #ddd;">下一页</a>
        <a href="{{ url_for('full_inspection.index', page=total_pages, per_page=per_page, start_date=start_date, end_date=end_date, date_type=date_type, material_number=material_number, supplier=supplier, quick_search=quick_search) }}" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f1f1f1; border: 1px solid #ddd;">末页</a>
        {% else %}
        <span class="disabled page-nav-btn next-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">下一页</span>
        <span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">末页</span>
        {% endif %}
    </div>
    
    <div class="page-size-selector" style="font-size: 11px; color: #666; display: flex; align-items: center; white-space: nowrap; min-width: auto; justify-content: flex-end; padding: 0; margin: 0;">
        每页:
        <select id="per-page-selector" onchange="changePageSize()" style="padding: 0 2px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px; margin-left: 3px; height: 20px; width: 45px;">
            <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
            <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
            <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
            <option value="200" {% if per_page == 200 %}selected{% endif %}>200</option>
        </select>
    </div>
</div>

<!-- 记录详情模态框 -->
<div id="record-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="document.getElementById('record-modal').style.display='none'">&times;</span>
        <h2 style="font-size: 13px; margin: 0 0 5px 0; border-bottom: 1px solid #eee; padding-bottom: 3px;">检验记录详情</h2>
        <div class="record-details">
            <!-- 详情内容将由JavaScript动态填充 -->
        </div>
    </div>
</div>

<!-- 高级搜索模态框 -->
<div id="advanced-search-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="document.getElementById('advanced-search-modal').style.display='none'">&times;</span>
        <h2 style="font-size: 13px; margin: 0 0 10px 0; border-bottom: 1px solid #eee; padding-bottom: 3px;">高级搜索</h2>
        
        <form id="advanced-search-form" method="get" action="{{ url_for('full_inspection.index') }}">
            <input type="hidden" name="date_type" id="advanced-date-type" value="{{ date_type }}">
            <div class="two-column-layout">
                <!-- 左侧列 -->
                <div class="column left-column">
                    <div class="form-group">
                        <div class="form-label">物料料号</div>
                        <div class="form-input">
                            <input type="text" name="material_number" value="{{ material_number or '' }}" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">供应商</div>
                        <div class="form-input">
                            <input type="text" name="supplier" value="{{ supplier or '' }}" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">采购单号</div>
                        <div class="form-input">
                            <input type="text" name="purchase_order" value="{{ purchase_order or '' }}" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">不良率</div>
                        <div class="form-input range-input">
                            <select name="defect_rate_op" class="form-control operator-select">
                                <option value="gt" {% if defect_rate_op == 'gt' %}selected{% endif %}>大于</option>
                                <option value="lte" {% if defect_rate_op == 'lte' %}selected{% endif %}>小于等于</option>
                            </select>
                            <input type="number" name="defect_rate" value="{{ defect_rate or '' }}" class="form-control" min="0" max="100" step="0.1">
                            <span class="unit-label">%</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">来料数量</div>
                        <div class="form-input range-input">
                            <select name="total_quantity_op" class="form-control operator-select">
                                <option value="gt" {% if total_quantity_op == 'gt' %}selected{% endif %}>大于</option>
                                <option value="lte" {% if total_quantity_op == 'lte' %}selected{% endif %}>小于等于</option>
                            </select>
                            <input type="number" name="total_quantity" value="{{ total_quantity or '' }}" class="form-control" min="0">
                        </div>
                    </div>
                </div>
                
                <!-- 右侧列 -->
                <div class="column right-column">
                    <div class="date-group">
                        <div class="date-label">来料日期</div>
                        <div class="date-inputs">
                            <div class="form-group">
                                <div class="form-label">开始</div>
                                <div class="form-input">
                                    <input type="date" name="receipt_start_date" value="{{ receipt_start_date or '' }}" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">结束</div>
                                <div class="form-input">
                                    <input type="date" name="receipt_end_date" value="{{ receipt_end_date or '' }}" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="date-group">
                        <div class="date-label">检验日期</div>
                        <div class="date-inputs">
                            <div class="form-group">
                                <div class="form-label">开始</div>
                                <div class="form-input">
                                    <input type="date" name="start_date" value="{{ start_date or '' }}" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">结束</div>
                                <div class="form-input">
                                    <input type="date" name="end_date" value="{{ end_date or '' }}" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="search-buttons">
                <button type="button" id="advanced-reset-btn" class="btn btn-secondary">重置</button>
                <button type="submit" class="btn btn-primary">搜索</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 重置按钮
        document.getElementById('reset-btn').addEventListener('click', function() {
            const form = document.getElementById('search-form');
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => {
                input.value = '';
            });
            form.submit();
        });

        // 页面大小选择器
        document.getElementById('per-page-selector').addEventListener('change', function() {
            changePageSize();
        });

        // 高级搜索按钮
        document.getElementById('advanced-search-btn').addEventListener('click', function() {
            const searchForm = document.getElementById('search-form');
            const currentUrl = new URL(window.location.href);
            const urlParams = currentUrl.searchParams;

            // 保存当前的URL参数
            const currentMaterialNumber = urlParams.get('material_number');
            const currentSupplier = urlParams.get('supplier');
            const currentStartDate = urlParams.get('start_date');
            const currentEndDate = urlParams.get('end_date');
            const currentPerPage = urlParams.get('per_page');
            const currentPage = urlParams.get('page');

            // 构建新的URL，移除所有参数，只保留分页参数
            const newUrl = `${window.location.pathname}?page=${currentPage}&per_page=${currentPerPage}`;

            // 将当前的URL参数添加到新的URL中
            if (currentMaterialNumber) {
                newUrl += `&material_number=${currentMaterialNumber}`;
            }
            if (currentSupplier) {
                newUrl += `&supplier=${currentSupplier}`;
            }
            if (currentStartDate) {
                newUrl += `&start_date=${currentStartDate}`;
            }
            if (currentEndDate) {
                newUrl += `&end_date=${currentEndDate}`;
            }

            // 打开新的URL
            window.location.href = newUrl;
        });
    });

    // 更改每页显示记录数
    function changePageSize() {
        const perPage = document.getElementById('per-page-selector').value;
        
        // 构建URL参数
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('per_page', perPage);
        urlParams.set('page', 1); // 切换页面大小后回到第一页
        
        // 重定向
        window.location.href = `${window.location.pathname}?${urlParams.toString()}`;
    }

    // 查看记录详情
    function viewInspectionDetails(recordId) {
        window.location.href = `/full_inspection/detail?id=${recordId}`;
    }
</script>
{% endblock %} 