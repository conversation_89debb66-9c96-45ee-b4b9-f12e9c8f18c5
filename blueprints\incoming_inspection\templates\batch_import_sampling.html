{% extends "base.html" %}

{% block title %}批量导入待检 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}{% endblock %}

{% block extra_css %}
<style>
    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e0e0e0;
    }

    .page-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
        font-weight: 600;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .btn-primary {
        background: #2196f3;
        color: white;
    }

    .btn-primary:hover {
        background: #1976d2;
    }

    .btn-success {
        background: #4caf50;
        color: white;
    }

    .btn-success:hover {
        background: #45a049;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
    }

    .import-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .import-methods {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    .import-method {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .import-method:hover {
        border-color: #2196f3;
        background: #f8f9fa;
    }

    .import-method.active {
        border-color: #2196f3;
        background: #e3f2fd;
    }

    .import-method i {
        font-size: 2em;
        color: #2196f3;
        margin-bottom: 10px;
    }

    .import-method h3 {
        margin: 10px 0;
        color: #333;
    }

    .import-method p {
        color: #666;
        font-size: 14px;
        margin: 0;
    }

    .import-content {
        display: none;
        margin-top: 20px;
    }

    .import-content.active {
        display: block;
    }

    .manual-input-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }

    .manual-input-table th,
    .manual-input-table td {
        padding: 10px;
        border: 1px solid #ddd;
        text-align: left;
    }

    .manual-input-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
    }

    .manual-input-table input,
    .manual-input-table select {
        width: 100%;
        padding: 6px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .manual-input-table input:focus,
    .manual-input-table select:focus {
        outline: none;
        border-color: #2196f3;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
    }

    .add-row-btn {
        background: #4caf50;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        margin-top: 10px;
    }

    .add-row-btn:hover {
        background: #45a049;
    }

    .remove-row-btn {
        background: #f44336;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
    }

    .remove-row-btn:hover {
        background: #d32f2f;
    }

    .file-upload-area {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        background: #fafafa;
        transition: all 0.3s;
    }

    .file-upload-area:hover {
        border-color: #2196f3;
        background: #f8f9fa;
    }

    .file-upload-area.dragover {
        border-color: #2196f3;
        background: #e3f2fd;
    }

    .file-upload-area i {
        font-size: 3em;
        color: #ccc;
        margin-bottom: 15px;
    }

    .file-upload-area h3 {
        margin: 15px 0 10px 0;
        color: #333;
    }

    .file-upload-area p {
        color: #666;
        margin: 0;
    }

    .file-input {
        display: none;
    }

    .upload-btn {
        background: #2196f3;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        margin-top: 15px;
    }

    .upload-btn:hover {
        background: #1976d2;
    }

    .template-download {
        margin-top: 15px;
    }

    .template-download a {
        color: #2196f3;
        text-decoration: none;
        font-size: 14px;
    }

    .template-download a:hover {
        text-decoration: underline;
    }

    .preview-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 30px;
        display: none;
    }

    .preview-section.show {
        display: block;
    }

    .preview-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }

    .preview-table th,
    .preview-table td {
        padding: 8px 12px;
        border: 1px solid #ddd;
        text-align: left;
        font-size: 14px;
    }

    .preview-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
    }

    .preview-table tr:nth-child(even) {
        background: #f9f9f9;
    }

    .status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-success {
        background: #e8f5e8;
        color: #2e7d32;
    }

    .status-warning {
        background: #fff3e0;
        color: #f57c00;
    }

    .status-error {
        background: #ffebee;
        color: #c62828;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 20px;
    }

    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        transform: translateX(0);
    }

    .toast.success {
        background: #4caf50;
    }

    .toast.error {
        background: #f44336;
    }

    .toast.warning {
        background: #ff9800;
    }

    .loading {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .loading.show {
        display: block;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #2196f3;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1><i class="fas fa-upload"></i> 批量导入待检 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}</h1>
        <div class="header-actions">
            <a href="{{ url_for('incoming_inspection.pending_list', type='sampling') }}" class="btn btn-secondary">
                <i class="fas fa-list"></i> 待检清单
            </a>
            <a href="{{ url_for('incoming_inspection.new_sampling_inspection') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增检验
            </a>
        </div>
    </div>

    <!-- 导入方式选择 -->
    <div class="import-section">
        <h2>选择导入方式</h2>
        <div class="import-methods">
            <div class="import-method active" data-method="manual">
                <i class="fas fa-keyboard"></i>
                <h3>手动录入</h3>
                <p>逐行输入物料信息，适合少量物料</p>
            </div>
            <div class="import-method" data-method="file">
                <i class="fas fa-file-excel"></i>
                <h3>文件导入</h3>
                <p>上传Excel文件批量导入，适合大量物料</p>
            </div>
        </div>

        <!-- 手动录入内容 -->
        <div class="import-content active" id="manual-content">
            <h3>手动录入物料信息</h3>
            <table class="manual-input-table" id="manual-table">
                <thead>
                    <tr>
                        <th width="120">物料料号 <span style="color: red;">*</span></th>
                        <th width="150">物料名称</th>
                        <th width="150">规格型号</th>
                        <th width="120">供应商</th>
                        <th width="100">来料数量</th>
                        <th width="60">单位</th>
                        <th width="100">批次号</th>
                        <th width="100">到货日期</th>
                        <th width="80">操作</th>
                    </tr>
                </thead>
                <tbody id="manual-tbody">
                    <tr>
                        <td><input type="text" class="material-code" placeholder="输入料号" onblur="fetchMaterialInfo(this)"></td>
                        <td><input type="text" class="material-name" placeholder="自动获取"></td>
                        <td><input type="text" class="specification" placeholder="自动获取"></td>
                        <td><input type="text" class="supplier-name" placeholder="自动获取"></td>
                        <td><input type="number" class="incoming-quantity" placeholder="数量" step="0.001"></td>
                        <td><input type="text" class="unit" placeholder="单位"></td>
                        <td><input type="text" class="batch-number" placeholder="批次号"></td>
                        <td><input type="date" class="arrival-date"></td>
                        <td><button type="button" class="remove-row-btn" onclick="removeRow(this)">删除</button></td>
                    </tr>
                </tbody>
            </table>
            <button type="button" class="add-row-btn" onclick="addRow()">
                <i class="fas fa-plus"></i> 添加行
            </button>
        </div>

        <!-- 文件导入内容 -->
        <div class="import-content" id="file-content">
            <h3>文件导入</h3>
            <div class="file-upload-area" id="upload-area">
                <i class="fas fa-cloud-upload-alt"></i>
                <h3>拖拽文件到此处或点击上传</h3>
                <p>支持 .xlsx, .xls 格式文件</p>
                <button type="button" class="upload-btn" onclick="document.getElementById('file-input').click()">
                    选择文件
                </button>
                <input type="file" id="file-input" class="file-input" accept=".xlsx,.xls" onchange="handleFileSelect(this)">
            </div>
            <div class="template-download">
                <a href="#" onclick="downloadTemplate()">
                    <i class="fas fa-download"></i> 下载导入模板
                </a>
            </div>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn btn-secondary" onclick="clearData()">
                <i class="fas fa-trash"></i> 清空数据
            </button>
            <button type="button" class="btn btn-primary" onclick="previewData()">
                <i class="fas fa-eye"></i> 预览数据
            </button>
        </div>
    </div>

    <!-- 数据预览区域 -->
    <div class="preview-section" id="preview-section">
        <h2>数据预览</h2>
        <div id="preview-content">
            <!-- 预览内容将在这里动态生成 -->
        </div>
        <div class="action-buttons">
            <button type="button" class="btn btn-secondary" onclick="hidePreview()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" class="btn btn-success" onclick="submitData()">
                <i class="fas fa-save"></i> 确认导入
            </button>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <p>正在处理，请稍候...</p>
    </div>
</div>

<!-- Toast 提示 -->
<div id="toast" class="toast"></div>
{% endblock %}

{% block extra_js %}
<script>
    let currentMethod = 'manual';
    let previewData = [];

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializeEventListeners();
    });

    function initializeEventListeners() {
        // 导入方式切换
        document.querySelectorAll('.import-method').forEach(method => {
            method.addEventListener('click', function() {
                switchImportMethod(this.dataset.method);
            });
        });

        // 文件拖拽
        const uploadArea = document.getElementById('upload-area');
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
    }

    function switchImportMethod(method) {
        currentMethod = method;
        
        // 更新方式选择样式
        document.querySelectorAll('.import-method').forEach(m => m.classList.remove('active'));
        document.querySelector(`[data-method="${method}"]`).classList.add('active');
        
        // 切换内容显示
        document.querySelectorAll('.import-content').forEach(c => c.classList.remove('active'));
        document.getElementById(`${method}-content`).classList.add('active');
    }

    function addRow() {
        const tbody = document.getElementById('manual-tbody');
        const newRow = tbody.rows[0].cloneNode(true);
        
        // 清空新行的输入值
        newRow.querySelectorAll('input').forEach(input => {
            input.value = '';
        });
        
        tbody.appendChild(newRow);
    }

    function removeRow(btn) {
        const tbody = document.getElementById('manual-tbody');
        if (tbody.rows.length > 1) {
            btn.closest('tr').remove();
        } else {
            showToast('至少保留一行数据', 'warning');
        }
    }

    async function fetchMaterialInfo(input) {
        const materialCode = input.value.trim();
        if (!materialCode) return;

        const row = input.closest('tr');
        
        try {
            const response = await fetch(`/material_management/api/materials/${materialCode}`);
            const data = await response.json();
            
            if (data.success && data.material) {
                const material = data.material;
                row.querySelector('.material-name').value = material.name || '';
                row.querySelector('.specification').value = material.specification || '';
                row.querySelector('.unit').value = material.unit || '';
                
                // 获取最近供应商
                const supplierResponse = await fetch(`/pending_inspection/api/latest_supplier/${materialCode}`);
                const supplierData = await supplierResponse.json();
                if (supplierData.success && supplierData.supplier) {
                    row.querySelector('.supplier-name').value = supplierData.supplier;
                }
            }
        } catch (error) {
            console.error('获取物料信息失败:', error);
        }
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    function handleDragLeave(e) {
        e.currentTarget.classList.remove('dragover');
    }

    function handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect({ files: files });
        }
    }

    function handleFileSelect(input) {
        const file = input.files[0];
        if (!file) return;

        if (!file.name.match(/\.(xlsx|xls)$/)) {
            showToast('请选择Excel文件', 'error');
            return;
        }

        // 这里可以添加文件解析逻辑
        showToast('文件上传成功，请点击预览数据', 'success');
    }

    function downloadTemplate() {
        // 创建模板数据
        const templateData = [
            ['物料料号', '物料名称', '规格型号', '供应商', '来料数量', '单位', '批次号', '到货日期'],
            ['示例001', '示例物料', '10*20*30', '示例供应商', '100', 'PCS', 'BATCH001', '2024-01-01']
        ];
        
        // 这里可以使用库如 SheetJS 来生成Excel文件
        showToast('模板下载功能开发中', 'warning');
    }

    function clearData() {
        if (currentMethod === 'manual') {
            const tbody = document.getElementById('manual-tbody');
            tbody.innerHTML = `
                <tr>
                    <td><input type="text" class="material-code" placeholder="输入料号" onblur="fetchMaterialInfo(this)"></td>
                    <td><input type="text" class="material-name" placeholder="自动获取"></td>
                    <td><input type="text" class="specification" placeholder="自动获取"></td>
                    <td><input type="text" class="supplier-name" placeholder="自动获取"></td>
                    <td><input type="number" class="incoming-quantity" placeholder="数量" step="0.001"></td>
                    <td><input type="text" class="unit" placeholder="单位"></td>
                    <td><input type="text" class="batch-number" placeholder="批次号"></td>
                    <td><input type="date" class="arrival-date"></td>
                    <td><button type="button" class="remove-row-btn" onclick="removeRow(this)">删除</button></td>
                </tr>
            `;
        } else {
            document.getElementById('file-input').value = '';
        }
        
        hidePreview();
        showToast('数据已清空', 'success');
    }

    function previewData() {
        if (currentMethod === 'manual') {
            previewManualData();
        } else {
            previewFileData();
        }
    }

    function previewManualData() {
        const rows = document.querySelectorAll('#manual-tbody tr');
        previewData = [];
        
        rows.forEach(row => {
            const materialCode = row.querySelector('.material-code').value.trim();
            if (materialCode) {
                previewData.push({
                    material_code: materialCode,
                    material_name: row.querySelector('.material-name').value.trim(),
                    specification: row.querySelector('.specification').value.trim(),
                    supplier_name: row.querySelector('.supplier-name').value.trim(),
                    incoming_quantity: row.querySelector('.incoming-quantity').value,
                    unit: row.querySelector('.unit').value.trim(),
                    batch_number: row.querySelector('.batch-number').value.trim(),
                    arrival_date: row.querySelector('.arrival-date').value
                });
            }
        });
        
        if (previewData.length === 0) {
            showToast('请至少输入一个物料料号', 'warning');
            return;
        }
        
        showPreview();
    }

    function previewFileData() {
        // 文件数据预览逻辑
        showToast('文件预览功能开发中', 'warning');
    }

    function showPreview() {
        const previewContent = document.getElementById('preview-content');
        
        let html = `
            <p>共 ${previewData.length} 条记录</p>
            <table class="preview-table">
                <thead>
                    <tr>
                        <th>物料料号</th>
                        <th>物料名称</th>
                        <th>规格型号</th>
                        <th>供应商</th>
                        <th>来料数量</th>
                        <th>单位</th>
                        <th>批次号</th>
                        <th>到货日期</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        previewData.forEach(item => {
            const status = item.material_code ? 'success' : 'error';
            const statusText = item.material_code ? '正常' : '料号为空';
            
            html += `
                <tr>
                    <td>${item.material_code || ''}</td>
                    <td>${item.material_name || ''}</td>
                    <td>${item.specification || ''}</td>
                    <td>${item.supplier_name || ''}</td>
                    <td>${item.incoming_quantity || ''}</td>
                    <td>${item.unit || ''}</td>
                    <td>${item.batch_number || ''}</td>
                    <td>${item.arrival_date || ''}</td>
                    <td><span class="status-badge status-${status}">${statusText}</span></td>
                </tr>
            `;
        });
        
        html += '</tbody></table>';
        previewContent.innerHTML = html;
        
        document.getElementById('preview-section').classList.add('show');
    }

    function hidePreview() {
        document.getElementById('preview-section').classList.remove('show');
    }

    async function submitData() {
        if (previewData.length === 0) {
            showToast('没有数据可提交', 'warning');
            return;
        }

        showLoading();
        
        try {
            const response = await fetch('/pending_inspection/api/pending_inspections/batch_import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inspection_type: '{{ inspection_type }}',
                    materials: previewData,
                    batch_name: `{{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}`
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                showToast(`导入成功！成功导入 ${data.data.success_count} 个物料`, 'success');
                
                // 3秒后跳转到待检清单
                setTimeout(() => {
                    window.location.href = '/incoming_inspection/pending_list?type={{ inspection_type }}';
                }, 3000);
            } else {
                showToast(`导入失败：${data.error}`, 'error');
            }
        } catch (error) {
            showToast('导入失败，请重试', 'error');
            console.error('导入失败:', error);
        } finally {
            hideLoading();
        }
    }

    function showLoading() {
        document.getElementById('loading').classList.add('show');
    }

    function hideLoading() {
        document.getElementById('loading').classList.remove('show');
    }

    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = `toast ${type} show`;
        
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }
</script>
{% endblock %}
