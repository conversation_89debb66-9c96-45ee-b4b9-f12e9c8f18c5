from flask import Flask, render_template, request, jsonify
import os
from db_config import get_db_connection
from config import config

def register_blueprints(app):
    # Import blueprints here to avoid circular imports
    from blueprints.incoming_inspection import incoming_inspection_bp, full_inspection_bp, sampling_inspection_bp
    from blueprints.process_control import process_control_bp
    from blueprints.system_settings import system_settings_bp
    from blueprints.material_management import material_management_bp
    from blueprints.dimension_measurement import dimension_measurement_bp
    from blueprints.batch_import.api import batch_import_bp

    # Register all blueprints here
    app.register_blueprint(material_management_bp)
    app.register_blueprint(incoming_inspection_bp)
    app.register_blueprint(full_inspection_bp)
    app.register_blueprint(sampling_inspection_bp)
    app.register_blueprint(process_control_bp)
    app.register_blueprint(system_settings_bp)
    app.register_blueprint(dimension_measurement_bp)
    app.register_blueprint(batch_import_bp, url_prefix='/batch_import')

def create_app(config_name='default'):
    app = Flask(__name__)

    # 加载配置
    app.config.from_object(config[config_name])

    # 确保上传目录存在
    if not os.path.exists(app.config['UPLOAD_FOLDER']):
        os.makedirs(app.config['UPLOAD_FOLDER'])
    
    # 注册蓝图
    with app.app_context():
        register_blueprints(app)
    
    return app

if __name__ == '__main__':
    # 获取环境变量，默认为开发环境
    config_name = os.environ.get('FLASK_ENV', 'development')
    app = create_app(config_name)

    # 注册额外的路由
    @app.route('/')
    def index():
        return render_template('index.html')

    @app.route('/api/material_info/<material_number>', methods=['GET'])
    def get_material_info(material_number):
        """获取物料信息API"""
        try:
            conn = get_db_connection()
            cursor = conn.cursor(dictionary=True)

            # 查询物料信息
            cursor.execute("""
                SELECT
                    material_number,
                    material_name,
                    specification,
                    material_type,
                    color,
                    material_category,
                    inspection_type
                FROM materials
                WHERE material_number = %s
            """, (material_number,))

            material = cursor.fetchone()

            if material:
                return jsonify({"success": True, "material": material})
            else:
                return jsonify({"success": False, "error": "未找到物料信息"}), 404

        except Exception as e:
            return jsonify({"success": False, "error": str(e)}), 500
        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    app.run(debug=True, host='0.0.0.0', port=5000)