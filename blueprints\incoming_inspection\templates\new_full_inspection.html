{% extends "base.html" %}

{% block title %}新增全部检验记录 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 页面整体布局 */
    .inspection-form-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    /* 表单区域布局 */
    .form-sections-wrapper {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 25px;
    }
    
    /* 表单区域样式 */
    .form-section {
        background: #fafafa;
        border-radius: 6px;
        padding: 20px;
        border: 1px solid #e0e0e0;
    }
    
    .form-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #1976d2;
        display: flex;
        align-items: center;
    }
    
    .form-section-title i {
        margin-right: 8px;
        font-size: 18px;
    }
    
    /* 表单组样式 */
    .form-group {
        margin-bottom: 12px;
        display: flex;
        align-items: center;
    }
    
    .form-label {
        width: 80px;
        text-align: right;
        padding-right: 12px;
        flex-shrink: 0;
        font-size: 13px;
        font-weight: 500;
        color: #333;
    }
    
    .form-input {
        flex: 1;
    }
    
    /* 输入框样式 */
    input[type="text"], 
    input[type="number"], 
    input[type="date"], 
    select, 
    textarea {
        width: 100%;
        height: 32px;
        padding: 6px 10px;
        font-size: 13px;
        border: 1px solid #ddd;
        border-radius: 4px;
        transition: border-color 0.3s ease;
        background: #fff;
        box-sizing: border-box;
    }
    
    input:focus, select:focus, textarea:focus {
        outline: none;
        border-color: #1976d2;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
    }
    
    input[readonly] {
        background-color: #f5f5f5;
        color: #666;
    }
    
    /* 输入组样式 */
    .input-group {
        display: flex;
        align-items: center;
    }
    
    .input-group-append {
        margin-left: 8px;
    }
    
    .input-group-append .btn {
        padding: 6px 12px;
        font-size: 12px;
        height: 32px;
        background: #1976d2;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }
    
    .input-group-append .btn:hover {
        background: #1565c0;
    }
    
    /* 不良问题记录区域 */
    .issues-section {
        grid-column: 1 / -1;
        background: #fff;
        border-radius: 6px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        margin-bottom: 20px;
    }
    
    .issues-section-title {
        font-size: 16px;
        font-weight: 600;
        color: #d32f2f;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 2px solid #d32f2f;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .issues-section-title .title-left {
        display: flex;
        align-items: center;
    }

    .issues-section-title .title-left i {
        margin-right: 8px;
        font-size: 18px;
    }

    /* 按钮组样式 */
    .button-group {
        display: flex;
        gap: 8px;
    }
    
    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-primary {
        background: #1976d2;
        color: white;
    }
    
    .btn-primary:hover {
        background: #1565c0;
    }
    
    .btn-danger {
        background: #d32f2f;
        color: white;
    }
    
    .btn-danger:hover {
        background: #c62828;
    }
    
    /* 表格样式 */
    .table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
        background: white;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .table th {
        background: #f5f5f5;
        padding: 10px 8px;
        font-size: 13px;
        font-weight: 600;
        color: #333;
        border-bottom: 2px solid #ddd;
        text-align: center;
    }
    
    .table td {
        padding: 0px;
        font-size: 14px;
        border-bottom: 1px solid #eee;
        vertical-align: middle;
        height: 12px !important;
        min-height: 12px !important;
    }
    
    .table tbody tr:hover {
        background-color: #f9f9f9;
    }
    
    /* 提交按钮区域 */
    .submit-section {
        grid-column: 1 / -1;
        text-align: center;
        padding: 20px 0;
        border-top: 1px solid #eee;
        margin-top: 20px;
    }
    
    .btn-submit {
        padding: 12px 30px;
        font-size: 14px;
        font-weight: 600;
        background: #4caf50;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
    }
    
    .btn-submit:hover {
        background: #45a049;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .form-sections-wrapper {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .inspection-form-container {
            padding: 15px;
            margin: 10px;
        }
        
        .form-section {
            padding: 15px;
        }
        
        .form-label {
            width: 70px;
            font-size: 12px;
        }
        
        input, select, textarea {
            font-size: 12px;
            height: 30px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1>新增全部检验记录</h1>
</div>

<div class="inspection-form-container">
    <form id="inspection-form" enctype="multipart/form-data">
        <!-- 基本信息和检验信息区域 -->
        <div class="form-sections-wrapper">
            <!-- 基本信息区域 -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-info-circle"></i>
                    基本信息
                </div>
                
                <div class="form-group">
                    <div class="form-label">物料料号</div>
                    <div class="form-input">
                        <div class="input-group">
                            <input type="text" id="material-number" required placeholder="请输入物料料号">
                            <div class="input-group-append">
                                <button type="button" id="fetch-material-btn" class="btn">查询</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">物料名称</div>
                    <div class="form-input">
                        <input type="text" id="material-name" required readonly placeholder="自动获取">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">规格</div>
                    <div class="form-input">
                        <input type="text" id="specification" readonly placeholder="自动获取">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">材质</div>
                    <div class="form-input">
                        <input type="text" id="material-type" readonly placeholder="自动获取">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">颜色</div>
                    <div class="form-input">
                        <input type="text" id="color" readonly placeholder="自动获取">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">供应商</div>
                    <div class="form-input">
                        <input type="text" id="supplier" required placeholder="请输入供应商名称">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">采购单号</div>
                    <div class="form-input">
                        <input type="text" id="purchase-order" required placeholder="请输入采购单号">
                    </div>
                </div>
            </div>
            
            <!-- 检验信息区域 -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-clipboard-check"></i>
                    检验信息
                </div>
                
                <div class="form-group">
                    <div class="form-label">来料日期</div>
                    <div class="form-input">
                        <input type="date" id="receipt-date" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">检验日期</div>
                    <div class="form-input">
                        <input type="date" id="inspection-date" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">来料数量</div>
                    <div class="form-input">
                        <input type="number" id="total-quantity" min="1" required placeholder="请输入来料总数量">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">合格数量</div>
                    <div class="form-input">
                        <input type="number" id="qualified-quantity" min="0" required placeholder="请输入合格数量">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">不良数量</div>
                    <div class="form-input">
                        <input type="number" id="defect-quantity" min="0" required placeholder="请输入不良数量">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">检验员</div>
                    <div class="form-input">
                        <input type="text" id="inspector" required placeholder="请输入检验员姓名">
                    </div>
                </div>
            </div>
        </div>

        <!-- 关键尺寸测量区域 -->
        <div id="dimension-measurement-section"></div>

        <!-- 不良问题记录区域 -->
        <div class="issues-section">
            <div class="issues-section-title">
                <div class="title-left">
                    <i class="fas fa-exclamation-triangle"></i>
                    不良问题记录
                </div>
                <div class="button-group">
                    <button type="button" id="add-issue-btn" class="btn-sm btn-primary">
                        <i class="fas fa-plus"></i> 添加问题点
                    </button>
                    <button type="button" id="remove-issue-btn" class="btn-sm btn-danger">
                        <i class="fas fa-minus"></i> 删除问题点
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table" id="issues-table">
                    <thead>
                        <tr>
                            <th width="8%">序号</th>
                            <th width="20%">问题类型</th>
                            <th width="42%">问题描述</th>
                            <th width="30%">图片上传（最多5张图片）</th>
                        </tr>
                    </thead>
                    <tbody id="issues-container">
                        <tr class="issue-row" data-index="1">
                            <td class="text-center">1</td>
                            <td>
                                <select class="issue-type" name="issue_type_1" required>
                                    <option value="">选择类型</option>
                                    <option value="尺寸不良">尺寸不良</option>
                                    <option value="外观不良">外观不良</option>
                                    <option value="功能不良">功能不良</option>
                                    <option value="包装不良">包装不良</option>
                                    <option value="其他">其他</option>
                                </select>
                            </td>
                            <td>
                                <textarea class="issue-description" name="issue_desc_1" rows="1" required placeholder="请详细描述问题"></textarea>
                            </td>
                            <td>
                                <div class="image-upload-container">
                                    <div class="image-preview-area"></div>
                                    <input type="file" class="issue-images" name="issue_images_1[]" multiple accept="image/*" data-max-files="5">
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 提交按钮区域 -->
        <div class="submit-section">
            <button type="submit" class="btn-submit">
                <i class="fas fa-save"></i> 提交全部检验记录
            </button>
        </div>
    </form>
</div>

<script src="{{ url_for('static', filename='js/dimension_measurement.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化日期为今天
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('inspection-date').value = today;
        document.getElementById('receipt-date').value = today;

        // 检查是否从批量导入待检记录跳转过来
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('material_code')) {
            // 预填充表单数据
            prefillFormFromParams(urlParams);
        }

        // 初始化关键尺寸测量功能
        let dimensionMeasurement = null;

        const materialNumberInput = document.getElementById('material-number');
        const materialNameInput = document.getElementById('material-name');
        const specificationInput = document.getElementById('specification');
        const materialTypeInput = document.getElementById('material-type');
        const colorInput = document.getElementById('color');

        // 获取物料信息
        document.getElementById('fetch-material-btn').addEventListener('click', function() {
            const materialNumber = materialNumberInput.value.trim();
            if (!materialNumber) {
                showAlert('请输入物料料号', 'error');
                return;
            }

            const btn = this;
            btn.disabled = true;
            btn.textContent = '查询中...';

            // 发送请求获取物料信息
            fetch(`/api/material_info/${materialNumber}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const material = data.material;
                        materialNameInput.value = material.material_name;
                        specificationInput.value = material.specification || '';
                        materialTypeInput.value = material.material_type || '';
                        colorInput.value = material.color || '';
                        showAlert('物料信息获取成功', 'success');

                        // 初始化关键尺寸测量功能
                        initializeDimensionMeasurement(materialNumber);
                    } else {
                        showAlert('未找到物料信息: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    showAlert('获取物料信息失败: ' + error, 'error');
                    console.error('获取物料信息错误:', error);
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.textContent = '查询';
                });
        });

        // 添加问题点
        document.getElementById('add-issue-btn').addEventListener('click', function() {
            const issuesContainer = document.getElementById('issues-container');
            const issueCount = issuesContainer.querySelectorAll('.issue-row').length;

            if (issueCount >= 10) {
                showAlert('最多只能添加10个问题点', 'error');
                return;
            }

            const newIndex = issueCount + 1;
            const newRow = document.createElement('tr');
            newRow.className = 'issue-row';
            newRow.setAttribute('data-index', newIndex);

            newRow.innerHTML = `
                <td class="text-center">${newIndex}</td>
                <td>
                    <select class="issue-type" name="issue_type_${newIndex}" required>
                        <option value="">选择类型</option>
                        <option value="尺寸不良">尺寸不良</option>
                        <option value="外观不良">外观不良</option>
                        <option value="功能不良">功能不良</option>
                        <option value="包装不良">包装不良</option>
                        <option value="其他">其他</option>
                    </select>
                </td>
                <td>
                    <textarea class="issue-description" name="issue_desc_${newIndex}" rows="1" required placeholder="请详细描述问题"></textarea>
                </td>
                <td>
                    <div class="image-upload-container">
                        <div class="image-preview-area"></div>
                        <input type="file" class="issue-images" name="issue_images_${newIndex}[]" multiple accept="image/*" data-max-files="5">
                    </div>
                </td>
            `;

            issuesContainer.appendChild(newRow);
        });

        // 删除问题点
        document.getElementById('remove-issue-btn').addEventListener('click', function() {
            const issuesContainer = document.getElementById('issues-container');
            const issueRows = issuesContainer.querySelectorAll('.issue-row');

            if (issueRows.length <= 1) {
                showAlert('至少保留一个问题点', 'error');
                return;
            }

            issuesContainer.removeChild(issueRows[issueRows.length - 1]);
        });

        // 初始化关键尺寸测量功能
        function initializeDimensionMeasurement(materialNumber) {
            if (dimensionMeasurement) {
                // 如果已经初始化，只更新物料编号并重新加载数据
                dimensionMeasurement.setMaterialNumber(materialNumber);
            } else {
                // 首次初始化
                dimensionMeasurement = new DimensionMeasurement('dimension-measurement-section', {
                    materialNumber: materialNumber,
                    inspectionType: 'full'
                });
            }
        }

        // 表单提交
        document.getElementById('inspection-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 验证必填字段
            if (!validateForm()) {
                return;
            }

            const submitBtn = document.querySelector('.btn-submit');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';

            // 构建表单数据
            const formData = new FormData();

            // 基本信息
            formData.append('inspection_type', 'full');
            formData.append('material_number', materialNumberInput.value);
            formData.append('material_name', materialNameInput.value);
            formData.append('specification', specificationInput.value);
            formData.append('material_type', materialTypeInput.value);
            formData.append('color', colorInput.value);
            formData.append('supplier', document.getElementById('supplier').value);
            formData.append('purchase_order', document.getElementById('purchase-order').value);
            formData.append('receipt_date', document.getElementById('receipt-date').value);
            formData.append('inspection_date', document.getElementById('inspection-date').value);
            formData.append('total_quantity', document.getElementById('total-quantity').value);
            formData.append('qualified_quantity', document.getElementById('qualified-quantity').value);
            formData.append('defect_quantity', document.getElementById('defect-quantity').value);
            formData.append('inspector', document.getElementById('inspector').value);

            // 问题点信息
            const issueRows = document.querySelectorAll('.issue-row');
            const issueData = [];

            issueRows.forEach((row, index) => {
                const rowIndex = index + 1;
                const issueType = row.querySelector('.issue-type').value;
                const issueDesc = row.querySelector('.issue-description').value;

                if (issueType && issueDesc) {
                    const issueInfo = {
                        type: issueType,
                        description: issueDesc
                    };

                    issueData.push(issueInfo);

                    // 添加图片文件
                    const imageFiles = row.querySelector('.issue-images').files;
                    for (let i = 0; i < imageFiles.length; i++) {
                        formData.append(`issue_images_${rowIndex}[]`, imageFiles[i]);
                    }
                }
            });

            formData.append('issues', JSON.stringify(issueData));

            // 发送请求
            fetch('/incoming/api/add_inspection', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(async data => {
                if (data.success) {
                    let message = data.report_code ?
                        `全部检验记录保存成功！报告编码：${data.report_code}` :
                        '全部检验记录保存成功！';

                    // 保存关键尺寸测量数据并自动更新模板
                    if (dimensionMeasurement && data.inspection_id) {
                        dimensionMeasurement.setInspectionInfo(data.inspection_id, 'full');
                        const dimensionResult = await dimensionMeasurement.saveMeasurements(true); // 传入true表示自动更新模板

                        if (dimensionResult.success) {
                            message += '\n关键尺寸测量数据保存成功！';
                            if (dimensionResult.message.includes('模板已自动更新')) {
                                message += '\n标准尺寸模板已自动更新！';
                            }
                        } else if (dimensionResult.error && !dimensionResult.error.includes('没有尺寸测量数据')) {
                            message += '\n关键尺寸测量数据保存失败: ' + dimensionResult.error;
                        }
                    }

                    showAlert(message, 'success');
                    // 延迟跳转，让用户看到成功提示
                    setTimeout(() => {
                        window.location.href = '/full_inspection/';
                    }, 2500);
                } else {
                    showAlert('保存失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showAlert('提交失败: ' + error, 'error');
                console.error('提交错误:', error);
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> 提交全部检验记录';
            });
        });

        // 表单验证函数
        function validateForm() {
            const requiredFields = [
                { id: 'material-number', name: '物料料号' },
                { id: 'material-name', name: '物料名称' },
                { id: 'supplier', name: '供应商' },
                { id: 'purchase-order', name: '采购单号' },
                { id: 'receipt-date', name: '来料日期' },
                { id: 'inspection-date', name: '检验日期' },
                { id: 'total-quantity', name: '来料数量' },
                { id: 'qualified-quantity', name: '合格数量' },
                { id: 'defect-quantity', name: '不良数量' },
                { id: 'inspector', name: '检验员' }
            ];

            for (const field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    showAlert(`请填写${field.name}`, 'error');
                    element.focus();
                    return false;
                }
            }

            // 验证数量逻辑
            const totalQty = parseInt(document.getElementById('total-quantity').value);
            const qualifiedQty = parseInt(document.getElementById('qualified-quantity').value);
            const defectQty = parseInt(document.getElementById('defect-quantity').value);

            if (qualifiedQty + defectQty !== totalQty) {
                showAlert('合格数量 + 不良数量 必须等于来料数量', 'error');
                return false;
            }

            if (qualifiedQty > totalQty) {
                showAlert('合格数量不能大于来料数量', 'error');
                return false;
            }

            if (defectQty > totalQty) {
                showAlert('不良数量不能大于来料数量', 'error');
                return false;
            }

            // 验证问题点
            const issueRows = document.querySelectorAll('.issue-row');
            for (let i = 0; i < issueRows.length; i++) {
                const row = issueRows[i];
                const issueType = row.querySelector('.issue-type').value;
                const issueDesc = row.querySelector('.issue-description').value.trim();

                if (!issueType || !issueDesc) {
                    showAlert(`请完整填写第${i + 1}个问题点的信息`, 'error');
                    return false;
                }
            }

            return true;
        }

        // 显示提示信息
        function showAlert(message, type) {
            // 移除现有的提示
            const existingAlert = document.querySelector('.alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // 创建新的提示
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.style.cssText = `
                padding: 10px 15px;
                margin: 10px 0;
                border-radius: 4px;
                font-size: 13px;
                ${type === 'success' ? 'background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724;' : 'background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'}
            `;
            alert.textContent = message;

            // 插入到表单顶部
            const form = document.getElementById('inspection-form');
            form.insertBefore(alert, form.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }

        // 从URL参数预填充表单
        function prefillFormFromParams(urlParams) {
            try {
                // 显示预填充提示
                showAlert('正在从待检记录预填充表单数据...', 'success');

                // 基础物料信息
                const materialCode = urlParams.get('material_code');
                const materialName = urlParams.get('material_name');
                const specification = urlParams.get('specification');
                const supplierName = urlParams.get('supplier_name');
                const incomingQuantity = urlParams.get('incoming_quantity');
                const unit = urlParams.get('unit');
                const notes = urlParams.get('notes');

                // 填充表单字段
                if (materialCode) {
                    document.getElementById('material-number').value = materialCode;
                }

                if (materialName) {
                    document.getElementById('material-name').value = materialName;
                }

                if (specification) {
                    document.getElementById('specification').value = specification;
                }

                if (supplierName) {
                    document.getElementById('supplier').value = supplierName;
                }

                if (incomingQuantity) {
                    document.getElementById('total-quantity').value = incomingQuantity;
                }

                if (unit) {
                    document.getElementById('unit').value = unit;
                }

                if (notes) {
                    document.getElementById('notes').value = notes;
                }

                // 显示成功提示
                setTimeout(() => {
                    showAlert('表单数据预填充完成，请核对并补充其他信息', 'success');
                }, 1000);

                // 清除URL参数，避免刷新页面时重复填充
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);

            } catch (error) {
                console.error('预填充表单失败:', error);
                showAlert('预填充表单失败，请手动输入', 'error');
            }
        }
    });
</script>
{% endblock %}
