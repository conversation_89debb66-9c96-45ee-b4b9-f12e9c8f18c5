#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, session, render_template
from database.connection import get_db_connection
import json
import uuid
from datetime import datetime

batch_import_bp = Blueprint('batch_import', __name__)

@batch_import_bp.route('/sampling_pending')
def sampling_pending():
    """抽样检验批量导入待检页面"""
    return render_template('batch_import_pending.html',
                         inspection_type='sampling',
                         inspection_type_name='抽样检验',
                         back_url='/sampling_inspection')

@batch_import_bp.route('/full_pending')
def full_pending():
    """全部检验批量导入待检页面"""
    return render_template('batch_import_pending.html',
                         inspection_type='full',
                         inspection_type_name='全部检验',
                         back_url='/full_inspection')

@batch_import_bp.route('/api/get_material_info/<material_code>')
def get_material_info(material_code):
    """根据料号获取物料基础信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取物料基础信息
        cursor.execute("""
            SELECT 
                material_code,
                material_name,
                specification,
                unit
            FROM materials 
            WHERE material_code = %s AND is_active = TRUE
        """, (material_code,))
        
        material = cursor.fetchone()
        
        if not material:
            return jsonify({
                "success": False,
                "error": "未找到该物料信息"
            }), 404
        
        # 获取最近一次检验的供应商
        cursor.execute("""
            SELECT supplier_name 
            FROM incoming_inspection 
            WHERE material_code = %s 
            ORDER BY created_at DESC 
            LIMIT 1
        """, (material_code,))
        
        recent_inspection = cursor.fetchone()
        supplier_name = recent_inspection['supplier_name'] if recent_inspection else None
        
        # 如果没有检验记录，尝试从物料信息中获取默认供应商
        if not supplier_name:
            cursor.execute("""
                SELECT supplier_name 
                FROM material_suppliers 
                WHERE material_code = %s AND is_active = TRUE
                ORDER BY created_at DESC 
                LIMIT 1
            """, (material_code,))
            
            supplier_info = cursor.fetchone()
            supplier_name = supplier_info['supplier_name'] if supplier_info else None
        
        return jsonify({
            "success": True,
            "data": {
                "material_code": material['material_code'],
                "material_name": material['material_name'],
                "specification": material['specification'],
                "unit": material['unit'],
                "supplier_name": supplier_name
            }
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@batch_import_bp.route('/api/batch_save_pending', methods=['POST'])
def batch_save_pending():
    """批量保存待检物料"""
    try:
        data = request.get_json()
        materials = data.get('materials', [])
        inspection_type = data.get('inspection_type')
        created_by = session.get('username', 'system')
        
        if not materials:
            return jsonify({
                "success": False,
                "error": "没有要保存的物料数据"
            }), 400
        
        if inspection_type not in ['sampling', 'full']:
            return jsonify({
                "success": False,
                "error": "检验类型无效"
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        batch_id = str(uuid.uuid4())[:8]
        success_count = 0
        failed_count = 0
        error_details = []
        
        for material in materials:
            try:
                # 检查必填字段
                if not material.get('material_code'):
                    failed_count += 1
                    error_details.append({
                        "material_code": material.get('material_code', ''),
                        "error": "物料料号不能为空"
                    })
                    continue
                
                # 检查物料是否已存在于待检列表中
                cursor.execute("""
                    SELECT id FROM batch_import_pending 
                    WHERE material_code = %s 
                    AND inspection_type = %s 
                    AND status = 'pending'
                """, (material['material_code'], inspection_type))
                
                if cursor.fetchone():
                    failed_count += 1
                    error_details.append({
                        "material_code": material['material_code'],
                        "error": "该物料已在待检列表中"
                    })
                    continue
                
                # 插入待检记录
                cursor.execute("""
                    INSERT INTO batch_import_pending (
                        material_code, material_name, specification, 
                        supplier_name, incoming_quantity, unit,
                        inspection_type, created_by, notes
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    material['material_code'],
                    material.get('material_name'),
                    material.get('specification'),
                    material.get('supplier_name'),
                    material.get('incoming_quantity'),
                    material.get('unit'),
                    inspection_type,
                    created_by,
                    material.get('notes')
                ))
                
                success_count += 1
                
            except Exception as e:
                failed_count += 1
                error_details.append({
                    "material_code": material.get('material_code', ''),
                    "error": str(e)
                })
        
        # 记录批量导入历史
        cursor.execute("""
            INSERT INTO batch_import_history (
                batch_id, import_count, success_count, failed_count,
                inspection_type, created_by, import_data, error_details
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            batch_id,
            len(materials),
            success_count,
            failed_count,
            inspection_type,
            created_by,
            json.dumps(materials, ensure_ascii=False),
            json.dumps(error_details, ensure_ascii=False) if error_details else None
        ))
        
        conn.commit()
        
        return jsonify({
            "success": True,
            "data": {
                "batch_id": batch_id,
                "total_count": len(materials),
                "success_count": success_count,
                "failed_count": failed_count,
                "error_details": error_details
            }
        })
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@batch_import_bp.route('/api/get_pending_list')
def get_pending_list():
    """获取待检物料列表"""
    try:
        inspection_type = request.args.get('inspection_type')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        if inspection_type not in ['sampling', 'full']:
            return jsonify({
                "success": False,
                "error": "检验类型无效"
            }), 400
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取总数
        cursor.execute("""
            SELECT COUNT(*) as total 
            FROM batch_import_pending 
            WHERE inspection_type = %s AND status = 'pending'
        """, (inspection_type,))
        
        total = cursor.fetchone()['total']
        
        # 获取分页数据
        offset = (page - 1) * per_page
        cursor.execute("""
            SELECT 
                id, material_code, material_name, specification,
                supplier_name, incoming_quantity, unit, notes,
                created_by, created_at
            FROM batch_import_pending 
            WHERE inspection_type = %s AND status = 'pending'
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """, (inspection_type, per_page, offset))
        
        materials = cursor.fetchall()
        
        return jsonify({
            "success": True,
            "data": {
                "materials": materials,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total": total,
                    "pages": (total + per_page - 1) // per_page
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@batch_import_bp.route('/api/update_pending/<int:pending_id>', methods=['PUT'])
def update_pending(pending_id):
    """更新待检物料信息"""
    try:
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("""
            SELECT id FROM batch_import_pending 
            WHERE id = %s AND status = 'pending'
        """, (pending_id,))
        
        if not cursor.fetchone():
            return jsonify({
                "success": False,
                "error": "待检记录不存在或已处理"
            }), 404
        
        # 更新记录
        cursor.execute("""
            UPDATE batch_import_pending SET
                material_name = %s,
                specification = %s,
                supplier_name = %s,
                incoming_quantity = %s,
                unit = %s,
                notes = %s,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (
            data.get('material_name'),
            data.get('specification'),
            data.get('supplier_name'),
            data.get('incoming_quantity'),
            data.get('unit'),
            data.get('notes'),
            pending_id
        ))
        
        conn.commit()
        
        return jsonify({
            "success": True,
            "message": "更新成功"
        })
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@batch_import_bp.route('/api/delete_pending/<int:pending_id>', methods=['DELETE'])
def delete_pending(pending_id):
    """删除待检物料"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("""
            SELECT id FROM batch_import_pending 
            WHERE id = %s AND status = 'pending'
        """, (pending_id,))
        
        if not cursor.fetchone():
            return jsonify({
                "success": False,
                "error": "待检记录不存在或已处理"
            }), 404
        
        # 删除记录
        cursor.execute("""
            DELETE FROM batch_import_pending WHERE id = %s
        """, (pending_id,))
        
        conn.commit()
        
        return jsonify({
            "success": True,
            "message": "删除成功"
        })
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

@batch_import_bp.route('/api/create_inspection_from_pending/<int:pending_id>', methods=['POST'])
def create_inspection_from_pending(pending_id):
    """从待检记录创建检验单"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 获取待检记录
        cursor.execute("""
            SELECT * FROM batch_import_pending 
            WHERE id = %s AND status = 'pending'
        """, (pending_id,))
        
        pending_record = cursor.fetchone()
        
        if not pending_record:
            return jsonify({
                "success": False,
                "error": "待检记录不存在或已处理"
            }), 404
        
        # 更新待检记录状态为处理中
        cursor.execute("""
            UPDATE batch_import_pending 
            SET status = 'processing', updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (pending_id,))
        
        conn.commit()
        
        # 返回待检记录信息，用于前端跳转到新增检验页面
        return jsonify({
            "success": True,
            "data": {
                "material_code": pending_record['material_code'],
                "material_name": pending_record['material_name'],
                "specification": pending_record['specification'],
                "supplier_name": pending_record['supplier_name'],
                "incoming_quantity": pending_record['incoming_quantity'],
                "unit": pending_record['unit'],
                "inspection_type": pending_record['inspection_type'],
                "notes": pending_record['notes']
            }
        })
        
    except Exception as e:
        if 'conn' in locals():
            conn.rollback()
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
