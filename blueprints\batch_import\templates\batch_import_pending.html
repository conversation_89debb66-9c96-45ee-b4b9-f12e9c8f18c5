{% extends "base.html" %}

{% block title %}批量导入待检 - {{ inspection_type_name }}{% endblock %}

{% block extra_css %}
<style>
    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e0e0e0;
    }

    .page-title {
        font-size: 24px;
        color: #333;
        font-weight: 600;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .btn-primary {
        background: #2196f3;
        color: white;
    }

    .btn-primary:hover {
        background: #1976d2;
    }

    .btn-success {
        background: #4caf50;
        color: white;
    }

    .btn-success:hover {
        background: #45a049;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
    }

    /* 批量输入区域 */
    .batch-input-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .section-title {
        font-size: 18px;
        color: #333;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .input-area {
        margin-bottom: 20px;
    }

    .input-area label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #555;
    }

    .input-area textarea {
        width: 100%;
        min-height: 120px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-family: monospace;
        font-size: 14px;
        resize: vertical;
    }

    .input-help {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }

    /* 待检列表区域 */
    .pending-list-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .search-box {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .search-box input {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        width: 200px;
    }

    /* 表格样式 */
    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .data-table th,
    .data-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .data-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .data-table tr:hover {
        background: #f8f9fa;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .btn-sm {
        padding: 5px 10px;
        font-size: 12px;
        border-radius: 3px;
    }

    .btn-edit {
        background: #ffc107;
        color: #212529;
    }

    .btn-edit:hover {
        background: #e0a800;
    }

    .btn-delete {
        background: #dc3545;
        color: white;
    }

    .btn-delete:hover {
        background: #c82333;
    }

    .btn-inspect {
        background: #17a2b8;
        color: white;
    }

    .btn-inspect:hover {
        background: #138496;
    }

    /* 分页样式 */
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 20px;
    }

    .pagination button {
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        cursor: pointer;
        border-radius: 3px;
    }

    .pagination button:hover {
        background: #f8f9fa;
    }

    .pagination button.active {
        background: #2196f3;
        color: white;
        border-color: #2196f3;
    }

    .pagination button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 20px;
        border-radius: 8px;
        width: 80%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    .close {
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        color: #999;
    }

    .close:hover {
        color: #333;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #555;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
    }

    .form-group textarea {
        min-height: 80px;
        resize: vertical;
    }

    /* 状态指示器 */
    .status-indicator {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }

    .status-processing {
        background: #d1ecf1;
        color: #0c5460;
    }

    .status-completed {
        background: #d4edda;
        color: #155724;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .page-container {
            padding: 10px;
        }
        
        .page-header {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
        }
        
        .header-actions {
            justify-content: center;
        }
        
        .data-table {
            font-size: 12px;
        }
        
        .data-table th,
        .data-table td {
            padding: 8px;
        }
        
        .modal-content {
            width: 95%;
            margin: 10% auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-list-ul"></i>
            批量导入待检 - {{ inspection_type_name }}
        </h1>
        <div class="header-actions">
            <a href="{{ back_url }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                返回{{ inspection_type_name }}
            </a>
        </div>
    </div>

    <!-- 批量输入区域 -->
    <div class="batch-input-section">
        <h2 class="section-title">
            <i class="fas fa-plus-circle"></i>
            批量添加待检物料
        </h2>
        
        <div class="input-area">
            <label for="material-codes">物料料号列表：</label>
            <textarea id="material-codes" placeholder="请输入物料料号，每行一个，例如：&#10;ABC001&#10;ABC002&#10;ABC003"></textarea>
            <div class="input-help">
                <i class="fas fa-info-circle"></i>
                每行输入一个物料料号，系统将自动获取物料基础信息
            </div>
        </div>
        
        <div style="text-align: center;">
            <button class="btn btn-primary" onclick="processMaterialCodes()">
                <i class="fas fa-search"></i>
                获取物料信息
            </button>
        </div>
    </div>

    <!-- 待检列表区域 -->
    <div class="pending-list-section">
        <div class="list-header">
            <h2 class="section-title">
                <i class="fas fa-clock"></i>
                待检物料列表
            </h2>
            <div class="search-box">
                <input type="text" id="search-input" placeholder="搜索物料料号或名称">
                <button class="btn btn-primary btn-sm" onclick="searchMaterials()">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <button class="btn btn-secondary btn-sm" onclick="refreshList()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
            </div>
        </div>

        <div class="table-container">
            <table class="data-table" id="pending-table">
                <thead>
                    <tr>
                        <th>物料料号</th>
                        <th>物料名称</th>
                        <th>规格型号</th>
                        <th>供应商</th>
                        <th>来料数量</th>
                        <th>单位</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="pending-tbody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <div class="pagination" id="pagination">
            <!-- 分页将通过JavaScript动态生成 -->
        </div>
    </div>
</div>

<!-- 编辑模态框 -->
<div id="edit-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">编辑待检物料</h3>
            <span class="close" onclick="closeEditModal()">&times;</span>
        </div>
        <form id="edit-form">
            <input type="hidden" id="edit-id">
            
            <div class="form-group">
                <label for="edit-material-code">物料料号：</label>
                <input type="text" id="edit-material-code" readonly>
            </div>
            
            <div class="form-group">
                <label for="edit-material-name">物料名称：</label>
                <input type="text" id="edit-material-name">
            </div>
            
            <div class="form-group">
                <label for="edit-specification">规格型号：</label>
                <input type="text" id="edit-specification">
            </div>
            
            <div class="form-group">
                <label for="edit-supplier-name">供应商：</label>
                <input type="text" id="edit-supplier-name">
            </div>
            
            <div class="form-group">
                <label for="edit-incoming-quantity">来料数量：</label>
                <input type="number" id="edit-incoming-quantity" step="0.01">
            </div>
            
            <div class="form-group">
                <label for="edit-unit">单位：</label>
                <input type="text" id="edit-unit">
            </div>
            
            <div class="form-group">
                <label for="edit-notes">备注：</label>
                <textarea id="edit-notes"></textarea>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button type="button" class="btn btn-success" onclick="saveEdit()">
                    <i class="fas fa-save"></i>
                    保存
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeEditModal()">
                    <i class="fas fa-times"></i>
                    取消
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    // 全局变量
    const inspectionType = '{{ inspection_type }}';
    let currentPage = 1;
    let totalPages = 1;
    let pendingMaterials = [];

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadPendingList();
    });

    // 处理物料料号输入
    function processMaterialCodes() {
        const materialCodes = document.getElementById('material-codes').value.trim();
        
        if (!materialCodes) {
            alert('请输入物料料号');
            return;
        }
        
        const codes = materialCodes.split('\n').map(code => code.trim()).filter(code => code);
        
        if (codes.length === 0) {
            alert('请输入有效的物料料号');
            return;
        }
        
        // 显示处理进度
        showProcessingProgress(codes);
    }

    // 显示处理进度并获取物料信息
    async function showProcessingProgress(codes) {
        const materials = [];
        let processed = 0;
        
        // 创建进度显示
        const progressHtml = `
            <div id="progress-container" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <div style="margin-bottom: 10px;">正在处理物料信息... (<span id="progress-count">0</span>/${codes.length})</div>
                <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                    <div id="progress-bar" style="background: #28a745; height: 100%; width: 0%; transition: width 0.3s;"></div>
                </div>
            </div>
        `;
        
        document.querySelector('.batch-input-section').insertAdjacentHTML('beforeend', progressHtml);
        
        // 逐个获取物料信息
        for (const code of codes) {
            try {
                const response = await fetch(`/batch_import/api/get_material_info/${encodeURIComponent(code)}`);
                const result = await response.json();
                
                if (result.success) {
                    materials.push({
                        ...result.data,
                        incoming_quantity: '',
                        notes: ''
                    });
                } else {
                    materials.push({
                        material_code: code,
                        material_name: '',
                        specification: '',
                        supplier_name: '',
                        unit: '',
                        incoming_quantity: '',
                        notes: `错误: ${result.error}`,
                        hasError: true
                    });
                }
            } catch (error) {
                materials.push({
                    material_code: code,
                    material_name: '',
                    specification: '',
                    supplier_name: '',
                    unit: '',
                    incoming_quantity: '',
                    notes: `错误: ${error.message}`,
                    hasError: true
                });
            }
            
            processed++;
            document.getElementById('progress-count').textContent = processed;
            document.getElementById('progress-bar').style.width = `${(processed / codes.length) * 100}%`;
        }
        
        // 移除进度显示
        document.getElementById('progress-container').remove();
        
        // 显示结果并保存
        showMaterialsPreview(materials);
    }

    // 显示物料预览并保存
    function showMaterialsPreview(materials) {
        if (materials.length === 0) {
            alert('没有有效的物料数据');
            return;
        }
        
        // 过滤掉有错误的物料
        const validMaterials = materials.filter(m => !m.hasError);
        const errorMaterials = materials.filter(m => m.hasError);
        
        let message = `找到 ${validMaterials.length} 个有效物料`;
        if (errorMaterials.length > 0) {
            message += `，${errorMaterials.length} 个物料有错误`;
        }
        message += '。是否保存到待检列表？';
        
        if (confirm(message)) {
            saveMaterialsToPending(validMaterials);
        }
        
        // 清空输入框
        document.getElementById('material-codes').value = '';
    }

    // 保存物料到待检列表
    async function saveMaterialsToPending(materials) {
        try {
            const response = await fetch('/batch_import/api/batch_save_pending', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    materials: materials,
                    inspection_type: inspectionType
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                const data = result.data;
                let message = `批量保存完成！\n成功: ${data.success_count} 个\n失败: ${data.failed_count} 个`;
                
                if (data.error_details && data.error_details.length > 0) {
                    message += '\n\n失败详情:\n';
                    data.error_details.forEach(error => {
                        message += `${error.material_code}: ${error.error}\n`;
                    });
                }
                
                alert(message);
                
                // 刷新列表
                loadPendingList();
            } else {
                alert('保存失败: ' + result.error);
            }
        } catch (error) {
            alert('保存失败: ' + error.message);
        }
    }

    // 加载待检列表
    async function loadPendingList(page = 1) {
        try {
            const response = await fetch(`/batch_import/api/get_pending_list?inspection_type=${inspectionType}&page=${page}&per_page=20`);
            const result = await response.json();
            
            if (result.success) {
                const data = result.data;
                pendingMaterials = data.materials;
                currentPage = data.pagination.page;
                totalPages = data.pagination.pages;
                
                renderPendingTable();
                renderPagination();
            } else {
                alert('加载列表失败: ' + result.error);
            }
        } catch (error) {
            alert('加载列表失败: ' + error.message);
        }
    }

    // 渲染待检表格
    function renderPendingTable() {
        const tbody = document.getElementById('pending-tbody');
        
        if (pendingMaterials.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; color: #999; padding: 40px;">
                        暂无待检物料
                    </td>
                </tr>
            `;
            return;
        }
        
        tbody.innerHTML = pendingMaterials.map(material => `
            <tr>
                <td>${material.material_code}</td>
                <td>${material.material_name || '-'}</td>
                <td>${material.specification || '-'}</td>
                <td>${material.supplier_name || '-'}</td>
                <td>${material.incoming_quantity || '-'}</td>
                <td>${material.unit || '-'}</td>
                <td>${new Date(material.created_at).toLocaleString()}</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-edit btn-sm" onclick="editMaterial(${material.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-inspect btn-sm" onclick="createInspection(${material.id})" title="创建检验">
                            <i class="fas fa-clipboard-check"></i>
                        </button>
                        <button class="btn btn-delete btn-sm" onclick="deleteMaterial(${material.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // 渲染分页
    function renderPagination() {
        const pagination = document.getElementById('pagination');
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // 上一页
        html += `<button ${currentPage <= 1 ? 'disabled' : ''} onclick="loadPendingList(${currentPage - 1})">上一页</button>`;
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === currentPage) {
                html += `<button class="active">${i}</button>`;
            } else {
                html += `<button onclick="loadPendingList(${i})">${i}</button>`;
            }
        }
        
        // 下一页
        html += `<button ${currentPage >= totalPages ? 'disabled' : ''} onclick="loadPendingList(${currentPage + 1})">下一页</button>`;
        
        pagination.innerHTML = html;
    }

    // 编辑物料
    function editMaterial(id) {
        const material = pendingMaterials.find(m => m.id === id);
        if (!material) {
            alert('物料不存在');
            return;
        }
        
        // 填充编辑表单
        document.getElementById('edit-id').value = material.id;
        document.getElementById('edit-material-code').value = material.material_code;
        document.getElementById('edit-material-name').value = material.material_name || '';
        document.getElementById('edit-specification').value = material.specification || '';
        document.getElementById('edit-supplier-name').value = material.supplier_name || '';
        document.getElementById('edit-incoming-quantity').value = material.incoming_quantity || '';
        document.getElementById('edit-unit').value = material.unit || '';
        document.getElementById('edit-notes').value = material.notes || '';
        
        // 显示模态框
        document.getElementById('edit-modal').style.display = 'block';
    }

    // 保存编辑
    async function saveEdit() {
        const id = document.getElementById('edit-id').value;
        const data = {
            material_name: document.getElementById('edit-material-name').value,
            specification: document.getElementById('edit-specification').value,
            supplier_name: document.getElementById('edit-supplier-name').value,
            incoming_quantity: document.getElementById('edit-incoming-quantity').value,
            unit: document.getElementById('edit-unit').value,
            notes: document.getElementById('edit-notes').value
        };
        
        try {
            const response = await fetch(`/batch_import/api/update_pending/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                alert('更新成功');
                closeEditModal();
                loadPendingList(currentPage);
            } else {
                alert('更新失败: ' + result.error);
            }
        } catch (error) {
            alert('更新失败: ' + error.message);
        }
    }

    // 关闭编辑模态框
    function closeEditModal() {
        document.getElementById('edit-modal').style.display = 'none';
    }

    // 删除物料
    async function deleteMaterial(id) {
        if (!confirm('确定要删除这个待检物料吗？')) {
            return;
        }
        
        try {
            const response = await fetch(`/batch_import/api/delete_pending/${id}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                alert('删除成功');
                loadPendingList(currentPage);
            } else {
                alert('删除失败: ' + result.error);
            }
        } catch (error) {
            alert('删除失败: ' + error.message);
        }
    }

    // 创建检验
    async function createInspection(id) {
        try {
            const response = await fetch(`/batch_import/api/create_inspection_from_pending/${id}`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                const data = result.data;
                
                // 构建跳转URL，带上物料信息参数
                const params = new URLSearchParams({
                    material_code: data.material_code,
                    material_name: data.material_name || '',
                    specification: data.specification || '',
                    supplier_name: data.supplier_name || '',
                    incoming_quantity: data.incoming_quantity || '',
                    unit: data.unit || '',
                    notes: data.notes || ''
                });
                
                let targetUrl;
                if (inspectionType === 'sampling') {
                    targetUrl = `/sampling_inspection/new?${params.toString()}`;
                } else {
                    targetUrl = `/full_inspection/new?${params.toString()}`;
                }
                
                // 跳转到新增检验页面
                window.location.href = targetUrl;
            } else {
                alert('创建检验失败: ' + result.error);
            }
        } catch (error) {
            alert('创建检验失败: ' + error.message);
        }
    }

    // 搜索物料
    function searchMaterials() {
        const searchTerm = document.getElementById('search-input').value.trim();
        // 这里可以实现搜索功能
        // 暂时使用简单的客户端过滤
        loadPendingList(1);
    }

    // 刷新列表
    function refreshList() {
        loadPendingList(currentPage);
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
        const modal = document.getElementById('edit-modal');
        if (event.target === modal) {
            closeEditModal();
        }
    }
</script>
{% endblock %}
