# 批量导入待检功能说明

## 📋 功能概述

批量导入待检功能允许用户提前批量输入需要检验的物料料号，系统自动获取物料基础信息，用户可以补充来料数量等信息，然后对单个物料进行编辑检验。

## 🎯 功能特点

### ✅ 主要功能
- **批量输入料号**：支持一次性输入多个物料料号
- **自动获取信息**：系统自动获取物料名称、规格、供应商等基础信息
- **智能供应商匹配**：优先使用最近一次检验的供应商，如无则查询物料默认供应商
- **待检列表管理**：统一管理所有待检物料，支持编辑、删除
- **一键创建检验**：从待检记录直接跳转到新增检验页面，自动预填充数据

### ✅ 支持的检验类型
- **抽样检验**：在抽样检验模块中使用
- **全部检验**：在全部检验模块中使用

## 🚀 使用流程

### 第一步：进入批量导入页面
1. **抽样检验**：进入抽样检验页面 → 点击"批量导入待检"按钮
2. **全部检验**：进入全部检验页面 → 点击"批量导入待检"按钮

### 第二步：批量输入物料料号
1. 在"物料料号列表"文本框中输入料号
2. 每行输入一个料号，例如：
   ```
   ABC001
   ABC002
   ABC003
   ```
3. 点击"获取物料信息"按钮

### 第三步：系统自动处理
1. **获取物料信息**：系统逐个查询物料基础信息
2. **显示进度**：实时显示处理进度
3. **匹配供应商**：自动匹配最近检验的供应商
4. **保存到待检列表**：成功的物料保存到待检列表

### 第四步：管理待检列表
1. **查看列表**：在"待检物料列表"中查看所有待检物料
2. **编辑信息**：点击"编辑"按钮修改物料信息
3. **补充数量**：添加来料数量等信息
4. **删除记录**：删除不需要的待检记录

### 第五步：创建检验
1. 点击待检记录的"创建检验"按钮
2. 系统自动跳转到新增检验页面
3. 表单自动预填充物料信息
4. 用户补充其他检验信息并保存

## 📊 数据字段说明

### 自动获取的字段
- **物料料号**：从输入的料号获取
- **物料名称**：从物料主数据获取
- **规格型号**：从物料主数据获取
- **单位**：从物料主数据获取
- **供应商**：优先从最近检验记录获取，其次从物料供应商信息获取

### 用户可编辑的字段
- **来料数量**：用户手动输入
- **备注**：用户手动输入
- **供应商**：可修改系统自动匹配的供应商

## 🔧 技术实现

### 数据库表结构

#### 批量导入待检表 (batch_import_pending)
```sql
- id: 主键
- material_code: 物料料号
- material_name: 物料名称
- specification: 规格型号
- supplier_name: 供应商名称
- incoming_quantity: 来料数量
- unit: 单位
- inspection_type: 检验类型 (sampling/full)
- status: 状态 (pending/processing/completed)
- created_by: 创建人
- created_at: 创建时间
- notes: 备注
```

#### 批量导入历史表 (batch_import_history)
```sql
- id: 主键
- batch_id: 批次ID
- import_count: 导入数量
- success_count: 成功数量
- failed_count: 失败数量
- inspection_type: 检验类型
- created_by: 创建人
- import_data: 导入数据详情 (JSON)
- error_details: 错误详情 (JSON)
```

### API接口

#### 获取物料信息
```
GET /batch_import/api/get_material_info/<material_code>
```

#### 批量保存待检
```
POST /batch_import/api/batch_save_pending
```

#### 获取待检列表
```
GET /batch_import/api/get_pending_list?inspection_type=sampling&page=1
```

#### 更新待检记录
```
PUT /batch_import/api/update_pending/<pending_id>
```

#### 删除待检记录
```
DELETE /batch_import/api/delete_pending/<pending_id>
```

#### 创建检验
```
POST /batch_import/api/create_inspection_from_pending/<pending_id>
```

## 🎨 用户界面

### 页面布局
1. **页面头部**：标题和返回按钮
2. **批量输入区域**：料号输入框和处理按钮
3. **待检列表区域**：表格显示所有待检物料
4. **编辑模态框**：弹窗编辑物料信息

### 操作按钮
- **获取物料信息**：处理输入的料号
- **编辑**：修改待检记录信息
- **创建检验**：跳转到新增检验页面
- **删除**：删除待检记录
- **搜索**：搜索待检列表
- **刷新**：刷新待检列表

## 📋 使用场景

### 场景1：日常来料批量登记
1. **背景**：每天有多个物料需要检验
2. **操作**：一次性输入所有料号，批量获取信息
3. **优势**：避免逐个新增检验时重复输入基础信息

### 场景2：计划性检验安排
1. **背景**：根据生产计划提前安排检验
2. **操作**：提前录入待检物料，后续按需创建检验
3. **优势**：提高检验计划性和效率

### 场景3：供应商来料集中处理
1. **背景**：同一供应商多个物料同时到货
2. **操作**：批量录入料号，统一设置供应商信息
3. **优势**：减少重复输入，提高数据一致性

## ⚠️ 注意事项

### 数据准确性
- **料号格式**：确保输入的料号格式正确
- **物料存在性**：系统只能获取已存在的物料信息
- **供应商匹配**：自动匹配的供应商可能需要手动确认

### 操作建议
- **分批处理**：建议每次处理20-50个料号
- **及时处理**：录入待检后应及时创建检验
- **定期清理**：定期清理已完成的待检记录

### 权限控制
- **创建权限**：需要有新增检验的权限
- **编辑权限**：只能编辑自己创建的待检记录
- **删除权限**：只能删除未处理的待检记录

## 🔄 状态流转

```
待检 (pending) → 处理中 (processing) → 已完成 (completed)
```

- **待检**：刚录入的待检记录
- **处理中**：已点击"创建检验"，正在编辑检验单
- **已完成**：检验单已保存完成

## 📈 效果评估

### 效率提升
- **录入效率**：批量录入比逐个新增快3-5倍
- **数据准确性**：自动获取基础信息，减少输入错误
- **工作流程**：优化检验前期准备工作

### 用户体验
- **操作简便**：一键批量处理
- **信息完整**：自动补全物料信息
- **流程顺畅**：无缝跳转到检验页面

---

**批量导入待检功能** - 让检验前期准备更高效！

如有任何问题，请联系系统管理员。
