{% extends "base.html" %}

{% block title %}新增抽样检验记录 - 品质中心管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 页面整体布局 */
    .inspection-form-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    /* 主要内容区域 - 三列布局 */
    .main-content-wrapper {
        display: grid;
        grid-template-columns: 1fr 1fr 400px;
        gap: 12px;
        margin-bottom: 15px;
    }
    
    /* 表单区域样式 */
    .form-section {
        background: #fafafa;
        border-radius: 2px;
        padding: 6px;
        border: 1px solid #e0e0e0;
    }
    
    .form-section-title {
        font-size: 13px;
        font-weight: 600;
        color: #1976d2;
        margin-bottom: 4px;
        padding-bottom: 2px;
        border-bottom: 2px solid #1976d2;
        display: flex;
        align-items: center;
    }
    
    .form-section-title i {
        margin-right: 4px;
        font-size: 18px;
    }
    
    /* 表单组样式 */
    .form-group {
        margin-bottom: 3px;
        display: flex;
        align-items: center;
    }
    
    .form-label {
        width: 60px;
        text-align: right;
        padding-right: 6px;
        flex-shrink: 0;
        font-size: 11px;
        font-weight: 500;
        color: #333;
    }
    
    .form-input {
        flex: 1;
    }
    
    /* 输入框样式 */
    input[type="text"],
    input[type="number"],
    input[type="date"],
    select,
    textarea {
        width: 100%;
        height: 24px;
        padding: 3px 6px;
        font-size: 11px;
        border: 1px solid #ddd;
        border-radius: 2px;
        transition: border-color 0.3s ease;
        background: #fff;
        box-sizing: border-box;
    }
    
    input:focus, select:focus, textarea:focus {
        outline: none;
        border-color: #1976d2;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
    }

    /* 规格字段悬停效果 */
    #specification:hover {
        background-color: #f0f8ff;
        border-color: #1976d2;
    }

    /* 规格提示框箭头 */
    #specification-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 20px;
        border: 5px solid transparent;
        border-top-color: #333;
    }

    /* 复制成功提示 */
    .copy-success {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #4caf50;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 2000;
        animation: fadeInOut 2s ease-in-out;
    }

    @keyframes fadeInOut {
        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
        20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
    }
    
    input[readonly] {
        background-color: #f5f5f5;
        color: #666;
    }
    
    /* 输入组样式 */
    .input-group {
        display: flex;
        align-items: center;
    }
    
    .input-group-append {
        margin-left: 8px;
    }
    
    .input-group-append .btn {
        padding: 3px 8px;
        font-size: 10px;
        height: 24px;
        background: #1976d2;
        color: white;
        border: none;
        border-radius: 2px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }
    
    .input-group-append .btn:hover {
        background: #1565c0;
    }
    
    /* 右侧信息面板 */
    .info-panel {
        background: #fff;
        border-radius: 2px;
        border: 1px solid #e0e0e0;
        height: 222px; /* 调整为与缩小后的基本信息容器高度一致 */
        display: flex;
        flex-direction: column;
        overflow: hidden; /* 防止内容溢出 */
    }

    .info-panel-title {
        font-size: 12px;
        font-weight: 600;
        color: #333;
        margin-bottom: 3px;
        padding: 6px 8px 0;
        display: flex;
        align-items: center;
    }
    
    .info-panel-title i {
        margin-right: 6px;
        font-size: 16px;
    }
    
    /* 历史问题点区域 */
    .history-issues-section {
        margin-bottom: 3px;
        height: 125px; /* 调整为125px以适应222px总高度 */
        display: flex;
        flex-direction: column;
        flex: 0 0 auto; /* 固定大小，不伸缩 */
    }

    .history-issues-title {
        color: #ff9800;
        border-bottom: 1px solid #ff9800;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 0;
        margin-bottom: 2px;
    }

    /* 标题右侧的时间筛选按钮 */
    .title-time-filter {
        display: flex;
        gap: 2px;
        align-items: center;
    }

    .time-btn {
        padding: 1px 4px;
        font-size: 8px;
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
    }

    .time-btn.active {
        background: #ff9800;
        color: white;
        border-color: #ff9800;
    }

    .time-btn:hover {
        border-color: #ff9800;
    }

    .expand-btn {
        padding: 1px 4px;
        font-size: 8px;
        background: #2196f3;
        color: white;
        border: none;
        border-radius: 2px;
        cursor: pointer;
        margin-left: 2px;
    }

    .expand-btn:hover {
        background: #1976d2;
    }

    /* 自定义时间范围 */
    .custom-date-range {
        display: none;
        padding: 6px 8px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 3px;
        margin: 2px 8px;
        gap: 3px;
        align-items: center;
        flex-wrap: wrap;
        font-size: 10px;
        position: relative;
        z-index: 100;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .custom-date-range input {
        height: 18px;
        font-size: 9px;
        padding: 1px 2px;
        border: 1px solid #ddd;
        border-radius: 2px;
    }

    .custom-date-range button {
        height: 18px;
        padding: 0 4px;
        font-size: 8px;
        background: #ff9800;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }

    /* 历史问题表格 */
    .history-table {
        flex: 1; /* 占用剩余空间 */
        overflow-y: auto; /* 垂直滚动 */
        margin: 0 8px 4px;
        border: 1px solid #e0e0e0;
        border-radius: 2px;
        background: #fff;
    }

    .history-table table {
        width: 100%;
        border-collapse: collapse;
        font-size: 12px;
    }

    .history-table th {
        background: #f8f8f8;
        background-color: #f8f8f8 !important;
        padding: 4px 3px;
        font-weight: 600;
        border-bottom: 1px solid #ddd;
        position: sticky;
        top: 0;
        font-size: 11px;
        z-index: 10;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .history-table td {
        padding: 3px;
        border-bottom: 1px solid #eee;
        vertical-align: top;
        font-size: 11px;
        line-height: 1.3;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 150px;
    }

    .history-table td:hover {
        background-color: #f0f8ff;
    }

    /* 历史问题表格提示框 */
    .history-tooltip {
        position: absolute;
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        max-width: 300px;
        word-wrap: break-word;
        z-index: 1000;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        display: none;
        bottom: 100%;
        left: 0;
        margin-bottom: 5px;
        white-space: normal;
    }

    .history-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 20px;
        border: 5px solid transparent;
        border-top-color: #333;
    }

    .history-table tr:hover {
        background-color: #f9f9f9;
    }

    /* 自定义滚动条样式 */
    .history-table::-webkit-scrollbar,
    .attachments-list::-webkit-scrollbar {
        width: 6px;
    }

    .history-table::-webkit-scrollbar-track,
    .attachments-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .history-table::-webkit-scrollbar-thumb,
    .attachments-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .history-table::-webkit-scrollbar-thumb:hover,
    .attachments-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 弹窗样式 */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        z-index: 1000;
        justify-content: center;
        align-items: center;
    }

    .modal-content {
        background: white;
        border-radius: 8px;
        width: 90%;
        max-width: 1000px;
        max-height: 80%;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .modal-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
    }

    .modal-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 20px;
        cursor: pointer;
        color: #666;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-close:hover {
        color: #333;
    }

    .modal-body {
        padding: 20px;
        overflow-y: auto;
        max-height: calc(80vh - 120px);
    }

    .modal-filter {
        margin-bottom: 8px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
    }

    .modal-filter label {
        font-size: 13px;
        font-weight: 500;
        margin-right: 5px;
    }

    .modal-filter input, .modal-filter select {
        height: 32px;
        padding: 4px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 12px;
    }

    .modal-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 13px;
    }

    .modal-table th {
        background: #f5f5f5;
        padding: 12px 8px;
        font-weight: 600;
        border-bottom: 2px solid #ddd;
        text-align: left;
        position: sticky;
        top: 0;
    }

    .modal-table td {
        padding: 10px 8px;
        border-bottom: 1px solid #eee;
        vertical-align: top;
    }

    .modal-table tr:hover {
        background-color: #f9f9f9;
    }

    .modal-table .date-col {
        width: 100px;
    }

    .modal-table .type-col {
        width: 120px;
    }

    .modal-table .desc-col {
        width: auto;
    }
    
    /* 附件信息区域 */
    .attachments-section {
        border-top: 1px solid #eee;
        margin-top: 3px;
        flex: 0 0 auto; /* 固定大小，不伸缩 */
        height: 75px; /* 调整为75px以适应222px总高度 */
        display: flex;
        flex-direction: column;
    }

    .attachments-title {
        color: #4caf50;
        border-bottom: 1px solid #4caf50;
        padding: 6px 0 0 0;
        margin-bottom: 3px;
    }

    .attachments-list {
        flex: 1; /* 占用剩余空间 */
        overflow-y: auto; /* 垂直滚动 */
        padding: 5px 8px;
        margin: 0;
        border: 1px solid #e0e0e0;
        border-radius: 2px;
        background: #fff;
        margin: 0 8px 4px;
    }

    .attachment-item {
        display: flex;
        flex-direction: column;
        padding: 6px 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 10px;
        margin: 0;
        line-height: 1.2;
    }

    .attachment-item:last-child {
        border-bottom: none;
    }

    .attachment-header {
        display: flex;
        align-items: center;
        margin-bottom: 2px;
    }

    .attachment-icon {
        margin-right: 5px;
        color: #666;
        width: 12px;
        flex-shrink: 0;
        font-size: 10px;
    }

    .attachment-name {
        flex: 1;
        color: #333;
        word-break: break-all;
        line-height: 1.2;
        margin-right: 5px;
        font-weight: 500;
    }

    .attachment-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 2px;
    }

    .attachment-meta {
        font-size: 8px;
        color: #666;
        flex-shrink: 0;
    }

    .attachment-link {
        color: #1976d2;
        text-decoration: none;
        font-size: 9px;
        padding: 1px 4px;
        border-radius: 2px;
        border: 1px solid #1976d2;
        flex-shrink: 0;
    }

    .attachment-link:hover {
        background-color: #1976d2;
        color: white;
        text-decoration: none;
    }
    

    
    .btn-sm {
        padding: 6px 12px;
        font-size: 12px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-primary {
        background: #1976d2;
        color: white;
    }
    
    .btn-primary:hover {
        background: #1565c0;
    }
    
    .btn-danger {
        background: #d32f2f;
        color: white;
    }
    
    .btn-danger:hover {
        background: #c62828;
    }
    
    /* 表格样式 */

    
    /* 提交按钮区域 */
    .submit-section {
        grid-column: 1 / -1;
        text-align: center;
        padding: 20px 0;
        border-top: 1px solid #eee;
        margin-top: 20px;
    }
    
    .btn-submit {
        padding: 12px 30px;
        font-size: 14px;
        font-weight: 600;
        background: #4caf50;
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
    }
    
    .btn-submit:hover {
        background: #45a049;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
    }
    
    /* 下载进度条样式 */
    .download-progress-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .download-progress-dialog {
        background: white;
        border-radius: 8px;
        padding: 20px;
        min-width: 350px;
        max-width: 450px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        animation: fadeInScale 0.3s ease-out;
    }

    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    .download-progress-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .download-progress-file {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
        word-break: break-all;
        background: #f8f9fa;
        padding: 8px 12px;
        border-radius: 4px;
        border-left: 3px solid #1976d2;
    }

    .download-progress-bar {
        width: 100%;
        height: 12px;
        background: #f0f0f0;
        border-radius: 6px;
        overflow: hidden;
        margin-bottom: 12px;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .download-progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #1976d2, #42a5f5, #1976d2);
        background-size: 200% 100%;
        border-radius: 6px;
        transition: width 0.3s ease;
        width: 0%;
        animation: progressShimmer 2s infinite;
    }

    @keyframes progressShimmer {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    .download-progress-text {
        font-size: 13px;
        color: #666;
        text-align: center;
        margin-bottom: 15px;
    }

    .download-progress-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
    }

    .download-progress-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        transition: all 0.2s ease;
        min-width: 80px;
    }

    .download-progress-btn.cancel {
        background: #f5f5f5;
        color: #666;
        border: 1px solid #ddd;
    }

    .download-progress-btn.cancel:hover {
        background: #e0e0e0;
    }

    .download-progress-btn.open {
        background: #1976d2;
        color: white;
    }

    .download-progress-btn.open:hover {
        background: #1565c0;
    }

    .download-progress-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .download-progress-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        color: #666;
    }

    .download-progress-status.success {
        color: #4caf50;
    }

    .download-progress-status.error {
        color: #f44336;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .main-content-wrapper {
            grid-template-columns: 1fr 1fr;
        }

        .info-panel {
            grid-column: 1 / -1;
            margin-top: 20px;
        }
    }
    
    @media (max-width: 768px) {
        .main-content-wrapper {
            grid-template-columns: 1fr;
            gap: 15px;
        }
        
        .inspection-form-container {
            padding: 15px;
            margin: 10px;
        }
        
        .form-section {
            padding: 15px;
        }
        
        .form-label {
            width: 70px;
            font-size: 12px;
        }
        
        input, select, textarea {
            font-size: 12px;
            height: 30px;
        }
    }

    /* 页面标题样式 */
    .page-header {
        margin-bottom: 3px;
        padding-bottom: 0;
    }

    .page-header h1 {
        margin: 0;
        padding: 0;
        font-size: 18px;
        color: #333;
        font-weight: 600;
    }

    /* 表单容器样式 */
    .inspection-form-container {
        margin-top: 0;
        padding-top: 0;
    }

    /* 浮动提示框动画 */
    @keyframes fadeInScale {
        0% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }
        100% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    }

    @keyframes fadeOutScale {
        0% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
        100% {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1>新增抽样检验记录</h1>
</div>

<div class="inspection-form-container">
    <form id="inspection-form" enctype="multipart/form-data">
        <div class="main-content-wrapper">
            <!-- 基本信息区域 -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-info-circle"></i>
                    基本信息
                </div>
                
                <div class="form-group">
                    <div class="form-label">物料料号</div>
                    <div class="form-input">
                        <input type="text" id="material-number" required placeholder="请输入物料料号（失去焦点时自动查询）">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">物料名称</div>
                    <div class="form-input">
                        <input type="text" id="material-name" required readonly placeholder="自动获取">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">规格</div>
                    <div class="form-input" style="position: relative;">
                        <input type="text" id="specification" readonly placeholder="自动获取" title="点击复制规格信息" style="cursor: pointer; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        <div id="specification-tooltip" style="display: none; position: absolute; background: #333; color: white; padding: 8px 12px; border-radius: 4px; font-size: 12px; max-width: 300px; word-wrap: break-word; z-index: 1000; box-shadow: 0 2px 8px rgba(0,0,0,0.2); bottom: 100%; left: 0; margin-bottom: 5px;"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">材质</div>
                    <div class="form-input">
                        <input type="text" id="material-type" readonly placeholder="自动获取">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">颜色</div>
                    <div class="form-input">
                        <input type="text" id="color" readonly placeholder="自动获取">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">物料类型</div>
                    <div class="form-input">
                        <input type="text" id="material-category" readonly placeholder="自动获取">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">供应商</div>
                    <div class="form-input">
                        <input type="text" id="supplier" required placeholder="请输入供应商名称">
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="form-label">采购单号</div>
                    <div class="form-input">
                        <input type="text" id="purchase-order" required placeholder="请输入采购单号">
                    </div>
                </div>
            </div>

            <!-- 检验信息区域 -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-clipboard-check"></i>
                    检验信息
                </div>

                <div class="form-group">
                    <div class="form-label">来料日期</div>
                    <div class="form-input">
                        <input type="date" id="receipt-date" required>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">检验日期</div>
                    <div class="form-input">
                        <input type="date" id="inspection-date" required>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">检验类型</div>
                    <div class="form-input">
                        <select id="inspection-type" required>
                            <option value="">请选择检验类型</option>
                            <option value="抽样">抽样检验</option>
                            <option value="全检">全部检验</option>
                            <option value="免检">免检</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">来料数量</div>
                    <div class="form-input">
                        <input type="number" id="total-quantity" min="1" required placeholder="请输入来料总数量">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">抽样数量</div>
                    <div class="form-input">
                        <input type="number" id="sample-quantity" min="1" required placeholder="请输入抽样数量">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">不良数量</div>
                    <div class="form-input">
                        <input type="number" id="defect-quantity" min="0" required placeholder="请输入不良数量">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">检验员</div>
                    <div class="form-input">
                        <input type="text" id="inspector" required placeholder="请输入检验员姓名">
                    </div>
                </div>
            </div>

            <!-- 右侧信息面板 -->
            <div class="info-panel">
                <!-- 历史问题点区域 -->
                <div class="history-issues-section">
                    <div class="info-panel-title history-issues-title">
                        <div style="display: flex; align-items: center;">
                            <i class="fas fa-history"></i>
                            历史问题点
                        </div>
                        <div class="title-time-filter">
                            <button type="button" class="time-btn active" data-period="1">1个月</button>
                            <button type="button" class="time-btn" data-period="3">3个月</button>
                            <button type="button" class="time-btn" data-period="6">半年</button>
                            <button type="button" class="time-btn" data-period="custom">自定义</button>
                            <button type="button" class="expand-btn" id="expand-history-btn">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 自定义时间范围 -->
                    <div class="custom-date-range" id="custom-date-range">
                        <label>开始日期:</label>
                        <input type="date" id="history-start-date">
                        <label>结束日期:</label>
                        <input type="date" id="history-end-date">
                        <button type="button" id="search-history-btn">查询</button>
                    </div>

                    <!-- 历史问题表格 -->
                    <div class="history-table" id="history-table">
                        <table>
                            <thead>
                                <tr>
                                    <th width="25%">日期</th>
                                    <th width="25%">问题类型</th>
                                    <th width="50%">问题描述</th>
                                </tr>
                            </thead>
                            <tbody id="history-issues-tbody">
                                <tr>
                                    <td colspan="3" style="text-align: center; color: #999; padding: 20px;">
                                        请先输入物料料号
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 附件信息区域 -->
                <div class="attachments-section">
                    <div class="info-panel-title attachments-title">
                        <i class="fas fa-paperclip"></i>
                        相关附件
                    </div>

                    <div class="attachments-list" id="attachments-list">
                        <div style="text-align: center; color: #999; padding: 20px;">
                            请先输入物料料号
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关键尺寸测量区域 -->
        <div id="dimension-measurement-section"></div>

        <!-- 不良问题记录区域 -->
        <div class="issues-section">
            <div class="issues-section-title">
                <div class="title-left">
                    <i class="fas fa-exclamation-triangle"></i>
                    不良问题记录
                </div>
                <div class="button-group">
                    <button type="button" id="add-issue-btn" class="btn-sm btn-primary">
                        <i class="fas fa-plus"></i> 添加问题点
                    </button>
                    <button type="button" id="remove-issue-btn" class="btn-sm btn-danger">
                        <i class="fas fa-minus"></i> 删除问题点
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table" id="issues-table">
                    <thead>
                        <tr>
                            <th width="8%">序号</th>
                            <th width="20%">问题类型</th>
                            <th width="42%">问题描述</th>
                            <th width="30%">图片上传（最多5张图片）</th>
                        </tr>
                    </thead>
                    <tbody id="issues-container">
                        <tr class="issue-row" data-index="1">
                            <td class="text-center">1</td>
                            <td>
                                <select class="issue-type" name="issue_type_1" required>
                                    <option value="">选择类型</option>
                                    <option value="尺寸不良">尺寸不良</option>
                                    <option value="外观不良">外观不良</option>
                                    <option value="功能不良">功能不良</option>
                                    <option value="包装不良">包装不良</option>
                                    <option value="其他">其他</option>
                                </select>
                            </td>
                            <td>
                                <textarea class="issue-description" name="issue_desc_1" rows="1" required placeholder="请详细描述问题"></textarea>
                            </td>
                            <td>
                                <div class="image-upload-container" data-row-index="1">
                                    <div class="image-add-btn" onclick="triggerImageUpload(this)">
                                        <i class="fas fa-plus"></i>
                                        <input type="file" accept="image/*" onchange="handleImageUpload(this)">
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 提交按钮区域 -->
        <div class="submit-section">
            <button type="submit" class="btn-submit">
                <i class="fas fa-save"></i> 提交抽样检验记录
            </button>
        </div>
    </form>
</div>

<!-- 历史问题点弹窗 -->
<div class="modal-overlay" id="history-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">历史问题点详情</h3>
            <button type="button" class="modal-close" id="close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <!-- 筛选器 -->
            <div class="modal-filter">
                <label>时间范围:</label>
                <select id="modal-period-select">
                    <option value="1">最近1个月</option>
                    <option value="3">最近3个月</option>
                    <option value="6">最近半年</option>
                    <option value="12">最近1年</option>
                    <option value="custom">自定义</option>
                </select>

                <div id="modal-custom-dates" style="display: none;">
                    <label>开始:</label>
                    <input type="date" id="modal-start-date">
                    <label>结束:</label>
                    <input type="date" id="modal-end-date">
                    <button type="button" id="modal-search-btn" class="time-btn">查询</button>
                </div>

                <label style="margin-left: 20px;">问题类型:</label>
                <select id="modal-type-filter">
                    <option value="">全部类型</option>
                    <option value="尺寸不良">尺寸不良</option>
                    <option value="外观不良">外观不良</option>
                    <option value="功能不良">功能不良</option>
                    <option value="包装不良">包装不良</option>
                    <option value="其他">其他</option>
                </select>

                <label style="margin-left: 20px;">关键词:</label>
                <input type="text" id="modal-keyword-filter" placeholder="搜索问题描述">
            </div>

            <!-- 统计信息 -->
            <div style="margin-bottom: 8px; padding: 6px; background: #e3f2fd; border-radius: 3px; font-size: 12px;">
                <strong>统计信息:</strong>
                <span id="modal-stats">共找到 0 条记录</span>
            </div>

            <!-- 表格 -->
            <div style="overflow-x: auto;">
                <table class="modal-table">
                    <thead>
                        <tr>
                            <th class="date-col">检验日期</th>
                            <th class="type-col">问题类型</th>
                            <th class="desc-col">问题描述</th>
                            <th style="width: 80px;">检验类型</th>
                            <th style="width: 100px;">供应商</th>
                        </tr>
                    </thead>
                    <tbody id="modal-history-tbody">
                        <tr>
                            <td colspan="5" style="text-align: center; color: #999; padding: 40px;">
                                暂无数据
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/dimension_measurement.js') }}"></script>
<script>
    // 全局函数：打开附件
    function openAttachment(fileName, url, fileExtension) {
        const extension = fileExtension.toLowerCase();

        // 可以在浏览器中直接打开的文件类型
        const inlineTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'txt', 'html', 'htm', 'xml', 'json'];

        if (inlineTypes.includes(extension)) {
            // 直接在浏览器中打开
            window.open(url, '_blank', 'noopener,noreferrer');
        } else {
            // 需要下载的文件类型，自动下载并尝试打开
            downloadAndOpenFile(url, fileName, extension);
        }
    }

    // 全局函数：自动下载并打开文件
    function downloadAndOpenFile(url, fileName, fileExtension) {
        // 显示下载进度对话框
        showDownloadProgressDialog(fileName, fileExtension);

        // 获取系统设置的下载路径，然后下载文件
        getDownloadPathAndDownload(url, fileName, fileExtension);
    }

    // 获取下载路径并下载文件
    function getDownloadPathAndDownload(sourceUrl, fileName, fileExtension) {
        // 首先获取系统设置的下载路径
        fetch('/system_settings/api/get_download_path')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const localPath = data.download_path;
                // 下载文件到本地路径
                downloadToLocalPath(sourceUrl, fileName, fileExtension, localPath);
            } else {
                // 使用默认路径
                const defaultPath = 'C:\\QMS1\\';
                downloadToLocalPath(sourceUrl, fileName, fileExtension, defaultPath);
            }
        })
        .catch(error => {
            console.error('获取下载路径失败:', error);
            // 使用默认路径
            const defaultPath = 'C:\\QMS1\\';
            downloadToLocalPath(sourceUrl, fileName, fileExtension, defaultPath);
        });
    }

    // 下载文件到本地路径
    function downloadToLocalPath(sourceUrl, fileName, fileExtension, localPath) {
        updateDownloadProgress(10, '正在准备下载...');

        // 解析附件ID和物料ID从URL
        // URL格式: /material_management/api/material/{material_id}/attachments/{attachment_id}/download
        const urlParts = sourceUrl.split('/').filter(part => part !== '');

        // 查找material和attachments的索引
        const materialIndex = urlParts.indexOf('material');
        const attachmentsIndex = urlParts.indexOf('attachments');

        if (materialIndex === -1 || attachmentsIndex === -1) {
            console.error('无法解析URL:', sourceUrl);
            updateDownloadProgress(0, '下载失败！');
            updateDownloadStatus('error', 'URL格式错误');
            return;
        }

        const materialId = urlParts[materialIndex + 1];
        const attachmentId = urlParts[attachmentsIndex + 1];

        console.log('解析URL:', {sourceUrl, materialId, attachmentId});

        // 调用物料管理的下载API
        const downloadUrl = `/material_management/api/material/${materialId}/attachments/${attachmentId}/download_to_local`;
        console.log('下载API URL:', downloadUrl);

        fetch(downloadUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                local_path: localPath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDownloadProgress(100, '下载完成！');
                updateDownloadStatus('success', '下载完成');

                // 启用自动打开按钮
                const openBtn = document.getElementById('open-file-btn');
                if (openBtn) {
                    openBtn.disabled = false;
                    openBtn.textContent = '自动打开';
                    openBtn.style.background = '#4caf50';
                }

                // 2秒后自动打开文件
                setTimeout(() => {
                    autoOpenLocalFile(data.local_file_path, fileName, fileExtension);
                }, 2000);
            } else {
                updateDownloadProgress(0, '下载失败！');
                updateDownloadStatus('error', '下载失败');

                setTimeout(() => {
                    closeDownloadProgress();
                    showToast('文件下载失败：' + data.error, 'error');
                }, 2000);
            }
        })
        .catch(error => {
            console.error('下载失败:', error);
            updateDownloadProgress(0, '下载失败！');
            updateDownloadStatus('error', '下载失败');

            setTimeout(() => {
                closeDownloadProgress();
                showToast('文件下载失败，请重试', 'error');
            }, 2000);
        });
    }

    // 自动打开本地文件
    function autoOpenLocalFile(filePath, fileName, fileExtension) {
        updateDownloadStatus('success', '正在打开文件...');

        // 调用后端API打开本地文件
        fetch('/sampling_inspection/api/open_local_file', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_path: filePath
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                setTimeout(() => {
                    closeDownloadProgress();
                    showToast(`文件 "${fileName}" 已自动打开！`, 'success');

                    // 显示打开建议
                    const openSuggestion = getOpenSuggestion(fileExtension);
                    if (openSuggestion) {
                        setTimeout(() => {
                            showToast(openSuggestion, 'info');
                        }, 1500);
                    }
                }, 1000);
            } else {
                setTimeout(() => {
                    closeDownloadProgress();
                    showToast('文件打开失败：' + data.error, 'error');
                    showManualOpenInstructions(fileName, fileExtension, filePath);
                }, 1000);
            }
        })
        .catch(error => {
            console.error('打开文件失败:', error);
            setTimeout(() => {
                closeDownloadProgress();
                showToast('文件打开失败，请手动打开', 'error');
                showManualOpenInstructions(fileName, fileExtension, filePath);
            }, 1000);
        });
    }

    // 显示下载进度对话框
    function showDownloadProgressDialog(fileName, fileExtension) {
        const overlay = document.createElement('div');
        overlay.className = 'download-progress-overlay';
        overlay.id = 'download-progress-overlay';

        overlay.innerHTML = `
            <div class="download-progress-dialog">
                <div class="download-progress-title">
                    <i class="fas fa-download"></i>
                    正在下载文件
                </div>
                <div class="download-progress-file">${fileName}</div>
                <div class="download-progress-bar">
                    <div class="download-progress-fill" id="download-progress-fill"></div>
                </div>
                <div class="download-progress-text" id="download-progress-text">准备下载...</div>
                <div class="download-progress-actions">
                    <div class="download-progress-status" id="download-progress-status">
                        <i class="fas fa-spinner fa-spin"></i>
                        下载中...
                    </div>
                    <div>
                        <button class="download-progress-btn cancel" onclick="cancelDownload()">取消</button>
                        <button class="download-progress-btn open" id="open-file-btn" onclick="openDownloadedFile('${fileName}', '${fileExtension}')" disabled>自动打开</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    // 使用fetch下载文件并显示进度
    function downloadFileWithProgress(url, fileName, fileExtension) {
        fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('下载失败');
            }

            const contentLength = response.headers.get('content-length');
            const total = parseInt(contentLength, 10);
            let loaded = 0;

            const reader = response.body.getReader();
            const chunks = [];

            function pump() {
                return reader.read().then(({ done, value }) => {
                    if (done) {
                        // 下载完成
                        const blob = new Blob(chunks);
                        const downloadUrl = window.URL.createObjectURL(blob);

                        // 创建下载链接
                        const downloadLink = document.createElement('a');
                        downloadLink.href = downloadUrl;
                        downloadLink.download = fileName;
                        downloadLink.style.display = 'none';

                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);

                        // 清理URL对象
                        window.URL.revokeObjectURL(downloadUrl);

                        // 更新进度条为完成状态
                        updateDownloadProgress(100, '下载完成！');
                        updateDownloadStatus('success', '下载完成');

                        // 启用自动打开按钮
                        const openBtn = document.getElementById('open-file-btn');
                        if (openBtn) {
                            openBtn.disabled = false;
                            openBtn.textContent = '自动打开';
                            openBtn.style.background = '#4caf50';
                        }

                        // 2秒后自动打开文件
                        setTimeout(() => {
                            autoOpenFile(fileName, fileExtension, url);
                        }, 2000);

                        return;
                    }

                    chunks.push(value);
                    loaded += value.length;

                    if (total) {
                        const progress = Math.round((loaded / total) * 100);
                        updateDownloadProgress(progress, `已下载 ${formatFileSize(loaded)} / ${formatFileSize(total)}`);
                    } else {
                        updateDownloadProgress(null, `已下载 ${formatFileSize(loaded)}`);
                    }

                    return pump();
                });
            }

            return pump();
        })
        .catch(error => {
            console.error('下载失败:', error);
            updateDownloadProgress(0, '下载失败！');
            updateDownloadStatus('error', '下载失败');

            // 显示错误提示
            setTimeout(() => {
                closeDownloadProgress();
                showToast('文件下载失败，请重试', 'error');
            }, 2000);
        });
    }

    // 更新下载进度
    function updateDownloadProgress(progress, text) {
        const progressFill = document.getElementById('download-progress-fill');
        const progressText = document.getElementById('download-progress-text');

        if (progressFill && progress !== null) {
            progressFill.style.width = progress + '%';
        }

        if (progressText) {
            progressText.textContent = text;
        }
    }

    // 更新下载状态
    function updateDownloadStatus(type, text) {
        const statusElement = document.getElementById('download-progress-status');
        if (statusElement) {
            statusElement.className = `download-progress-status ${type}`;
            if (type === 'success') {
                statusElement.innerHTML = `<i class="fas fa-check-circle"></i> ${text}`;
            } else if (type === 'error') {
                statusElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${text}`;
            } else {
                statusElement.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
            }
        }
    }

    // 自动打开文件
    function autoOpenFile(fileName, fileExtension, originalUrl) {
        updateDownloadStatus('success', '正在打开文件...');

        const extension = fileExtension.toLowerCase();

        // 尝试多种方式静默打开文件
        attemptSilentFileOpen(fileName, fileExtension, originalUrl);
    }

    // 尝试静默打开文件（不打开新界面）
    function attemptSilentFileOpen(fileName, fileExtension, originalUrl) {
        const extension = fileExtension.toLowerCase();

        // 方法1: 尝试使用文件协议直接打开
        if (tryFileProtocolOpen(fileName, extension)) {
            setTimeout(() => {
                closeDownloadProgress();
                showToast(`文件 "${fileName}" 正在打开...`, 'success');
            }, 1000);
            return;
        }

        // 方法2: 尝试使用应用程序协议
        if (tryApplicationProtocolOpen(fileName, extension)) {
            setTimeout(() => {
                closeDownloadProgress();
                showToast(`文件 "${fileName}" 正在用相应软件打开...`, 'success');
            }, 1000);
            return;
        }

        // 方法3: 对于PDF等文件，创建隐藏iframe预览
        if (['pdf'].includes(extension)) {
            if (tryIframePreview(originalUrl, fileName)) {
                setTimeout(() => {
                    closeDownloadProgress();
                    showToast(`文件 "${fileName}" 已在后台打开！`, 'success');
                }, 1000);
                return;
            }
        }

        // 方法4: 尝试使用系统默认程序
        if (trySystemDefaultOpen(fileName, extension)) {
            setTimeout(() => {
                closeDownloadProgress();
                showToast(`文件 "${fileName}" 正在用默认程序打开...`, 'success');
            }, 1000);
            return;
        }

        // 所有方法都失败，显示手动指导
        setTimeout(() => {
            closeDownloadProgress();
            showManualOpenInstructions(fileName, fileExtension);
        }, 1000);
    }

    // 尝试使用文件协议打开
    function tryFileProtocolOpen(fileName, extension) {
        try {
            // 构建文件路径
            const userProfile = navigator.userAgent.includes('Windows') ?
                'C:\\Users\\<USER>\d+\.\d+)/) ? 'User' : 'User') + '\\Downloads\\' :
                '/Users/' + (process.env.USER || 'user') + '/Downloads/';

            const filePath = userProfile + fileName;

            // 尝试使用file协议
            const fileUrl = 'file:///' + filePath.replace(/\\/g, '/');

            // 创建隐藏的iframe来尝试打开文件
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = fileUrl;
            document.body.appendChild(iframe);

            // 5秒后移除iframe
            setTimeout(() => {
                if (iframe.parentNode) {
                    iframe.parentNode.removeChild(iframe);
                }
            }, 5000);

            return true;
        } catch (e) {
            console.log('文件协议方法失败:', e);
            return false;
        }
    }

    // 尝试使用应用程序协议打开
    function tryApplicationProtocolOpen(fileName, extension) {
        try {
            const protocolMap = {
                'xlsx': 'ms-excel:ofe|u|',
                'xls': 'ms-excel:ofe|u|',
                'docx': 'ms-word:ofe|u|',
                'doc': 'ms-word:ofe|u|',
                'pptx': 'ms-powerpoint:ofe|u|',
                'ppt': 'ms-powerpoint:ofe|u|'
            };

            if (protocolMap[extension]) {
                // 构建下载路径
                const downloadsPath = navigator.userAgent.includes('Windows') ?
                    'C:\\Users\\<USER>\\Downloads\\' : '/Users/<USER>/Downloads/';

                const protocolUrl = protocolMap[extension] + downloadsPath + fileName;

                // 创建隐藏的iframe来触发协议
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = protocolUrl;
                document.body.appendChild(iframe);

                // 3秒后移除iframe
                setTimeout(() => {
                    if (iframe.parentNode) {
                        iframe.parentNode.removeChild(iframe);
                    }
                }, 3000);

                return true;
            }
        } catch (e) {
            console.log('应用程序协议方法失败:', e);
        }
        return false;
    }

    // 尝试使用iframe预览
    function tryIframePreview(originalUrl, fileName) {
        try {
            // 创建一个隐藏的iframe来预览PDF
            const iframe = document.createElement('iframe');
            iframe.src = originalUrl;
            iframe.style.position = 'fixed';
            iframe.style.top = '50px';
            iframe.style.right = '20px';
            iframe.style.width = '400px';
            iframe.style.height = '500px';
            iframe.style.border = '1px solid #ccc';
            iframe.style.borderRadius = '4px';
            iframe.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
            iframe.style.zIndex = '9999';
            iframe.style.background = 'white';

            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.position = 'absolute';
            closeBtn.style.top = '5px';
            closeBtn.style.right = '5px';
            closeBtn.style.width = '25px';
            closeBtn.style.height = '25px';
            closeBtn.style.border = 'none';
            closeBtn.style.background = '#f44336';
            closeBtn.style.color = 'white';
            closeBtn.style.borderRadius = '50%';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.fontSize = '14px';
            closeBtn.style.zIndex = '10000';

            closeBtn.onclick = function() {
                if (iframe.parentNode) iframe.parentNode.removeChild(iframe);
                if (closeBtn.parentNode) closeBtn.parentNode.removeChild(closeBtn);
            };

            document.body.appendChild(iframe);
            document.body.appendChild(closeBtn);

            // 10秒后自动关闭
            setTimeout(() => {
                if (iframe.parentNode) iframe.parentNode.removeChild(iframe);
                if (closeBtn.parentNode) closeBtn.parentNode.removeChild(closeBtn);
            }, 10000);

            return true;
        } catch (e) {
            console.log('iframe预览方法失败:', e);
            return false;
        }
    }

    // 尝试使用系统默认程序打开
    function trySystemDefaultOpen(fileName, extension) {
        try {
            // 对于Windows系统，尝试使用shell执行
            if (navigator.userAgent.includes('Windows')) {
                // 尝试使用ActiveX（仅在IE或特殊配置的浏览器中工作）
                if (window.ActiveXObject || "ActiveXObject" in window) {
                    const shell = new ActiveXObject("WScript.Shell");
                    const downloadsPath = shell.ExpandEnvironmentStrings("%USERPROFILE%\\Downloads\\");
                    shell.Run('"' + downloadsPath + fileName + '"');
                    return true;
                }

                // 尝试使用Windows Run协议
                const runUrl = 'ms-windows-store://pdp/?productid=9WZDNCRFJ3Q2&mode=mini&pos=0,0,0,0&url=' +
                              encodeURIComponent('file:///' + fileName);

                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.src = runUrl;
                document.body.appendChild(iframe);

                setTimeout(() => {
                    if (iframe.parentNode) {
                        iframe.parentNode.removeChild(iframe);
                    }
                }, 2000);

                return true;
            }
        } catch (e) {
            console.log('系统默认程序方法失败:', e);
        }
        return false;
    }

    // 尝试打开已下载文件的多种方式
    function attemptToOpenDownloadedFile(fileName, fileExtension) {
        const extension = fileExtension.toLowerCase();

        // 方法1: 尝试使用Windows shell命令（需要特殊权限）
        if (window.navigator.platform.indexOf('Win') !== -1) {
            try {
                // 尝试使用ActiveX对象（仅在IE或启用ActiveX的浏览器中工作）
                if (window.ActiveXObject || "ActiveXObject" in window) {
                    const shell = new ActiveXObject("WScript.Shell");
                    const downloadsPath = shell.ExpandEnvironmentStrings("%USERPROFILE%\\Downloads\\");
                    shell.Run(`"${downloadsPath}${fileName}"`);
                    return true;
                }
            } catch (e) {
                console.log('ActiveX方法失败:', e);
            }
        }

        // 方法2: 尝试使用文件系统访问API（现代浏览器）
        if ('showOpenFilePicker' in window) {
            try {
                // 这个API需要用户手势，但我们可以尝试
                showFileOpenDialog(fileName, extension);
                return true;
            } catch (e) {
                console.log('文件系统API方法失败:', e);
            }
        }

        // 方法3: 使用自定义协议（如果已注册）
        try {
            const protocolMap = {
                'xlsx': 'ms-excel:',
                'xls': 'ms-excel:',
                'docx': 'ms-word:',
                'doc': 'ms-word:',
                'pptx': 'ms-powerpoint:',
                'ppt': 'ms-powerpoint:',
                'pdf': 'ms-edge:'
            };

            if (protocolMap[extension]) {
                const downloadsPath = getDownloadsPath();
                window.location.href = `${protocolMap[extension]}ofe|u|${downloadsPath}${fileName}`;
                return true;
            }
        } catch (e) {
            console.log('协议方法失败:', e);
        }

        return false; // 所有方法都失败
    }

    // 获取下载文件夹路径
    function getDownloadsPath() {
        // 尝试获取用户的下载文件夹路径
        const userAgent = navigator.userAgent;
        if (userAgent.indexOf('Windows') !== -1) {
            return 'file:///C:/Users/' + (process.env.USERNAME || 'User') + '/Downloads/';
        } else if (userAgent.indexOf('Mac') !== -1) {
            return 'file:///Users/' + (process.env.USER || 'user') + '/Downloads/';
        } else {
            return 'file:///home/' + (process.env.USER || 'user') + '/Downloads/';
        }
    }

    // 显示文件打开对话框
    async function showFileOpenDialog(fileName, extension) {
        try {
            const fileHandle = await window.showOpenFilePicker({
                suggestedName: fileName,
                types: [{
                    description: `${extension.toUpperCase()} files`,
                    accept: {
                        [`application/${extension}`]: [`.${extension}`]
                    }
                }]
            });

            if (fileHandle && fileHandle[0]) {
                // 文件已选择，尝试打开
                const file = await fileHandle[0].getFile();
                const url = URL.createObjectURL(file);
                window.open(url, '_blank');
                return true;
            }
        } catch (e) {
            console.log('文件对话框失败:', e);
        }
        return false;
    }

    // 显示手动打开指导
    function showManualOpenInstructions(fileName, fileExtension, filePath) {
        const instructions = getDetailedOpenInstructions(fileExtension);

        // 创建详细的指导对话框
        const overlay = document.createElement('div');
        overlay.className = 'download-progress-overlay';
        overlay.id = 'manual-open-overlay';

        overlay.innerHTML = `
            <div class="download-progress-dialog" style="max-width: 500px;">
                <div class="download-progress-title">
                    <i class="fas fa-info-circle" style="color: #2196f3;"></i>
                    文件下载完成
                </div>
                <div class="download-progress-file">${fileName}</div>
                <div style="padding: 15px 0; font-size: 14px; line-height: 1.6; color: #333;">
                    <p><strong>文件已下载到本地QMS文件夹中。</strong></p>
                    <p><strong>文件位置：</strong></p>
                    <div style="background: #f8f9fa; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 5px 0;">
                        ${filePath || 'C:\\QMS1\\' + fileName}
                    </div>
                    <p><strong>打开方式：</strong></p>
                    <ol style="margin: 10px 0; padding-left: 20px;">
                        <li>打开文件管理器</li>
                        <li>导航到上述文件路径</li>
                        <li>双击文件"${fileName}"即可打开</li>
                    </ol>
                    <p style="background: #f0f7ff; padding: 10px; border-radius: 4px; border-left: 3px solid #2196f3;">
                        <i class="fas fa-lightbulb"></i> <strong>建议：</strong>${instructions}
                    </p>
                </div>
                <div class="download-progress-actions" style="justify-content: center;">
                    <button class="download-progress-btn open" onclick="closeManualInstructions()" style="background: #4caf50;">
                        <i class="fas fa-check"></i> 我知道了
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);
    }

    // 获取详细的打开指导
    function getDetailedOpenInstructions(fileExtension) {
        const instructions = {
            'xlsx': '使用 Microsoft Excel、WPS表格 或 Google Sheets 打开',
            'xls': '使用 Microsoft Excel、WPS表格 或 LibreOffice Calc 打开',
            'docx': '使用 Microsoft Word、WPS文字 或 Google Docs 打开',
            'doc': '使用 Microsoft Word、WPS文字 或 LibreOffice Writer 打开',
            'pptx': '使用 Microsoft PowerPoint、WPS演示 或 Google Slides 打开',
            'ppt': '使用 Microsoft PowerPoint、WPS演示 或 LibreOffice Impress 打开',
            'pdf': '使用 Adobe Reader、浏览器 或 其他PDF阅读器打开',
            'dwg': '使用 AutoCAD、DWG Viewer 或 免费的CAD查看器打开',
            'dxf': '使用 AutoCAD、FreeCAD 或 其他CAD软件打开'
        };

        return instructions[fileExtension] || '使用相应的软件打开此文件';
    }

    // 关闭手动指导对话框
    function closeManualInstructions() {
        const overlay = document.getElementById('manual-open-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // 取消下载
    function cancelDownload() {
        closeDownloadProgress();
        showToast('下载已取消', 'info');
    }

    // 关闭下载进度对话框
    function closeDownloadProgress() {
        const overlay = document.getElementById('download-progress-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // 打开下载的文件
    function openDownloadedFile(fileName, fileExtension) {
        autoOpenFile(fileName, fileExtension);
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    // 全局函数：获取文件打开建议
    function getOpenSuggestion(fileExtension) {
        const suggestions = {
            'dwg': '建议使用 AutoCAD 或 DWG Viewer 打开 DWG 文件',
            'dxf': '建议使用 AutoCAD 或 DXF Viewer 打开 DXF 文件',
            'doc': '建议使用 Microsoft Word 打开 DOC 文件',
            'docx': '建议使用 Microsoft Word 打开 DOCX 文件',
            'xls': '建议使用 Microsoft Excel 打开 XLS 文件',
            'xlsx': '建议使用 Microsoft Excel 打开 XLSX 文件',
            'ppt': '建议使用 Microsoft PowerPoint 打开 PPT 文件',
            'pptx': '建议使用 Microsoft PowerPoint 打开 PPTX 文件',
            'step': '建议使用 CAD 软件打开 STEP 文件',
            'stp': '建议使用 CAD 软件打开 STP 文件',
            'iges': '建议使用 CAD 软件打开 IGES 文件',
            'igs': '建议使用 CAD 软件打开 IGS 文件'
        };

        return suggestions[fileExtension] || null;
    }

    // 全局函数：显示Toast提示
    function showToast(message, type) {
        // 如果页面有showAlert函数，使用它
        if (typeof showAlert === 'function') {
            showAlert(message, type);
            return;
        }

        // 否则使用简单的alert
        alert(message);
    }

    document.addEventListener('DOMContentLoaded', function() {
        // 初始化日期为今天
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('inspection-date').value = today;
        document.getElementById('receipt-date').value = today;

        // 恢复从其他页面跳转过来的数据
        restoreFormDataFromSession();

        // 初始化关键尺寸测量功能
        let dimensionMeasurement = null;

        const materialNumberInput = document.getElementById('material-number');
        const materialNameInput = document.getElementById('material-name');
        const specificationInput = document.getElementById('specification');
        const materialTypeInput = document.getElementById('material-type');
        const colorInput = document.getElementById('color');
        const materialCategoryInput = document.getElementById('material-category');
        const inspectionTypeInput = document.getElementById('inspection-type');

        // 获取物料信息 - 失去焦点时自动查询
        function fetchMaterialInfo() {
            const materialNumber = materialNumberInput.value.trim();
            if (!materialNumber) {
                return; // 空值时不执行查询
            }

            // 显示加载状态
            materialNameInput.value = '查询中...';
            specificationInput.value = '查询中...';
            materialTypeInput.value = '查询中...';
            colorInput.value = '查询中...';
            materialCategoryInput.value = '查询中...';
            inspectionTypeInput.value = '';

            // 发送请求获取物料信息
            fetch(`/api/material_info/${materialNumber}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const material = data.material;
                        materialNameInput.value = material.material_name;
                        specificationInput.value = material.specification || '';
                        materialTypeInput.value = material.material_type || '';
                        colorInput.value = material.color || '';
                        materialCategoryInput.value = material.material_category || '';

                        // 设置检验类型并检查是否需要跳转
                        const materialInspectionType = material.inspection_type || '';
                        inspectionTypeInput.value = materialInspectionType;

                        // 检查检验类型是否匹配
                        checkInspectionTypeMatch(materialInspectionType);

                        showAlert('物料信息获取成功', 'success');

                        // 获取历史问题点和附件信息
                        loadHistoryIssues(materialNumber);
                        loadAttachments(materialNumber);

                        // 自动检索最近的供应商信息
                        fetchRecentSupplier(materialNumber);

                        // 初始化关键尺寸测量功能
                        initializeDimensionMeasurement(materialNumber);
                    } else {
                        // 清空字段并显示错误
                        materialNameInput.value = '';
                        specificationInput.value = '';
                        materialTypeInput.value = '';
                        colorInput.value = '';
                        materialCategoryInput.value = '';
                        inspectionTypeInput.value = '';
                        showAlert('未找到物料信息: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    // 清空字段并显示错误
                    materialNameInput.value = '';
                    specificationInput.value = '';
                    materialTypeInput.value = '';
                    colorInput.value = '';
                    showAlert('获取物料信息失败: ' + error, 'error');
                    console.error('获取物料信息错误:', error);
                });
        }

        // 监听物料料号输入框失去焦点事件
        materialNumberInput.addEventListener('blur', fetchMaterialInfo);

        // 监听回车键事件
        materialNumberInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.blur(); // 触发失去焦点事件
            }
        });

        // 规格字段特殊功能
        const specificationTooltip = document.getElementById('specification-tooltip');
        let tooltipTimeout;

        // 鼠标悬停显示完整规格信息
        specificationInput.addEventListener('mouseenter', function() {
            const fullText = this.value.trim();
            if (fullText && fullText !== '查询中...' && fullText !== '自动获取') {
                specificationTooltip.textContent = fullText;
                specificationTooltip.style.display = 'block';
            }
        });

        // 鼠标离开隐藏提示框
        specificationInput.addEventListener('mouseleave', function() {
            tooltipTimeout = setTimeout(() => {
                specificationTooltip.style.display = 'none';
            }, 200);
        });

        // 鼠标进入提示框时保持显示
        specificationTooltip.addEventListener('mouseenter', function() {
            clearTimeout(tooltipTimeout);
        });

        // 鼠标离开提示框时隐藏
        specificationTooltip.addEventListener('mouseleave', function() {
            this.style.display = 'none';
        });

        // 点击复制规格信息
        specificationInput.addEventListener('click', function() {
            const textToCopy = this.value.trim();
            if (textToCopy && textToCopy !== '查询中...' && textToCopy !== '自动获取') {
                // 使用现代的 Clipboard API
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(textToCopy).then(() => {
                        showCopySuccess();
                    }).catch(() => {
                        // 降级到传统方法
                        fallbackCopyText(textToCopy);
                    });
                } else {
                    // 降级到传统方法
                    fallbackCopyText(textToCopy);
                }
            }
        });

        // 传统复制方法（兼容性）
        function fallbackCopyText(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopySuccess();
            } catch (err) {
                console.error('复制失败:', err);
                showAlert('复制失败，请手动复制', 'error');
            } finally {
                document.body.removeChild(textArea);
            }
        }

        // 显示复制成功提示
        function showCopySuccess() {
            const successDiv = document.createElement('div');
            successDiv.className = 'copy-success';
            successDiv.textContent = '规格信息已复制到剪贴板';
            document.body.appendChild(successDiv);

            setTimeout(() => {
                if (document.body.contains(successDiv)) {
                    document.body.removeChild(successDiv);
                }
            }, 2000);
        }

        // 时间筛选按钮事件
        document.querySelectorAll('.time-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const period = this.dataset.period;
                const customDateRange = document.getElementById('custom-date-range');

                // 更新按钮状态
                document.querySelectorAll('.time-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                if (period === 'custom') {
                    customDateRange.style.display = 'flex';
                } else {
                    customDateRange.style.display = 'none';
                    // 自动加载对应时间段的历史问题
                    const materialNumber = materialNumberInput.value.trim();
                    if (materialNumber) {
                        loadHistoryIssues(materialNumber, period);
                    }
                }
            });
        });

        // 展开历史问题弹窗
        document.getElementById('expand-history-btn').addEventListener('click', function() {
            const materialNumber = materialNumberInput.value.trim();
            if (!materialNumber) {
                showAlert('请先输入物料料号', 'error');
                return;
            }

            openHistoryModal(materialNumber);
        });

        // 自定义时间范围查询
        document.getElementById('search-history-btn').addEventListener('click', function() {
            const materialNumber = materialNumberInput.value.trim();
            const startDate = document.getElementById('history-start-date').value;
            const endDate = document.getElementById('history-end-date').value;
            const customDateRange = document.getElementById('custom-date-range');

            if (!materialNumber) {
                showAlert('请先输入物料料号', 'error');
                return;
            }

            if (!startDate || !endDate) {
                showAlert('请选择开始和结束日期', 'error');
                return;
            }

            // 执行查询
            loadHistoryIssues(materialNumber, 'custom', startDate, endDate);

            // 查询完成后隐藏自定义日期范围
            customDateRange.style.display = 'none';

            // 更新按钮状态，移除所有按钮的active状态，因为现在是自定义范围
            document.querySelectorAll('.time-btn').forEach(btn => btn.classList.remove('active'));
        });

        // 弹窗相关事件
        document.getElementById('close-modal').addEventListener('click', closeHistoryModal);
        document.getElementById('history-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeHistoryModal();
            }
        });

        // 弹窗筛选器事件
        document.getElementById('modal-period-select').addEventListener('change', function() {
            const customDates = document.getElementById('modal-custom-dates');
            if (this.value === 'custom') {
                customDates.style.display = 'flex';
            } else {
                customDates.style.display = 'none';
                loadModalHistoryData();
            }
        });

        document.getElementById('modal-search-btn').addEventListener('click', loadModalHistoryData);
        document.getElementById('modal-type-filter').addEventListener('change', filterModalData);
        document.getElementById('modal-keyword-filter').addEventListener('input', filterModalData);

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeHistoryModal();
            }
        });

        // 加载历史问题点
        function loadHistoryIssues(materialNumber, period = '1', startDate = '', endDate = '') {
            const tbody = document.getElementById('history-issues-tbody');
            tbody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #999; padding: 20px;">加载中...</td></tr>';

            let url = `/incoming/api/history_issues/${materialNumber}?period=${period}`;
            if (period === 'custom' && startDate && endDate) {
                url += `&start_date=${startDate}&end_date=${endDate}`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.issues.length > 0) {
                        tbody.innerHTML = data.issues.map(issue => `
                            <tr>
                                <td title="${issue.inspection_date}">${issue.inspection_date}</td>
                                <td title="${issue.issue_type}">${issue.issue_type}</td>
                                <td title="${issue.issue_description}">${issue.issue_description}</td>
                            </tr>
                        `).join('');

                        // 为新生成的表格单元格添加悬停提示功能
                        addHistoryTableTooltips();
                    } else {
                        tbody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #999; padding: 20px;">暂无历史问题记录</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('加载历史问题失败:', error);
                    tbody.innerHTML = '<tr><td colspan="3" style="text-align: center; color: #f44336; padding: 20px;">加载失败</td></tr>';
                });
        }

        // 初始化关键尺寸测量功能
        function initializeDimensionMeasurement(materialNumber) {
            if (dimensionMeasurement) {
                // 如果已经初始化，只更新物料编号并重新加载数据
                dimensionMeasurement.setMaterialNumber(materialNumber);
            } else {
                // 首次初始化
                dimensionMeasurement = new DimensionMeasurement('dimension-measurement-section', {
                    materialNumber: materialNumber,
                    inspectionType: 'sampling'
                });
            }
        }

        // 获取最近的供应商信息
        function fetchRecentSupplier(materialNumber) {
            const supplierInput = document.getElementById('supplier');

            // 显示加载状态
            supplierInput.value = '查询中...';

            // 发送请求获取最近的验货记录
            fetch(`/incoming/api/recent_supplier/${materialNumber}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.supplier) {
                        supplierInput.value = data.supplier;
                        showAlert('已自动填入最近的供应商信息', 'success');
                    } else {
                        // 如果没有找到历史供应商，清空字段
                        supplierInput.value = '';
                        // 不显示错误提示，因为这是正常情况（新物料可能没有历史记录）
                    }
                })
                .catch(error => {
                    // 查询失败时清空字段，不显示错误提示
                    supplierInput.value = '';
                    console.log('获取供应商信息失败:', error);
                });
        }

        // 检查检验类型是否匹配
        function checkInspectionTypeMatch(materialInspectionType) {
            const currentPageType = '抽样'; // 当前页面是抽样检验

            if (materialInspectionType && materialInspectionType !== currentPageType) {
                let message = '';
                let redirectUrl = '';

                if (materialInspectionType === '全检') {
                    message = `该物料设置为"全部检验"，当前页面是"抽样检验"。\n是否跳转到全部检验页面？`;
                    redirectUrl = '/incoming_inspection/new_full_inspection';
                } else if (materialInspectionType === '免检') {
                    message = `该物料设置为"免检"，当前页面是"抽样检验"。\n免检物料无需进行检验。`;
                    redirectUrl = null;
                }

                if (message) {
                    // 使用自定义确认对话框
                    showInspectionTypeDialog(message, redirectUrl, materialInspectionType);
                }
            }
        }

        // 显示检验类型不匹配对话框
        function showInspectionTypeDialog(message, redirectUrl, inspectionType) {
            // 先移除可能存在的旧对话框
            const existingDialog = document.getElementById('inspection-type-dialog');
            if (existingDialog) {
                existingDialog.remove();
            }

            // 创建对话框容器
            const dialogContainer = document.createElement('div');
            dialogContainer.id = 'inspection-type-dialog';
            dialogContainer.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.7); z-index: 10000; display: flex;
                align-items: center; justify-content: center;
            `;

            // 创建对话框内容
            const dialogContent = document.createElement('div');
            dialogContent.style.cssText = `
                background: #2d2d2d; padding: 30px; border-radius: 8px;
                max-width: 500px; width: 90%; border: 1px solid #404040;
                box-shadow: 0 4px 20px rgba(0,0,0,0.5);
            `;

            // 创建标题区域
            const titleArea = document.createElement('div');
            titleArea.style.cssText = 'text-align: center; margin-bottom: 20px;';
            titleArea.innerHTML = `
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #ff9800; margin-bottom: 15px;"></i>
                <h3 style="color: #4a9eff; margin: 0 0 15px;">检验类型不匹配</h3>
            `;

            // 创建消息区域
            const messageArea = document.createElement('div');
            messageArea.style.cssText = 'color: #e0e0e0; line-height: 1.6; margin-bottom: 25px; text-align: center;';
            messageArea.innerHTML = message.replace(/\n/g, '<br>');

            // 创建按钮区域
            const buttonArea = document.createElement('div');
            buttonArea.style.cssText = 'text-align: center;';

            if (redirectUrl) {
                // 创建跳转按钮
                const redirectBtn = document.createElement('button');
                redirectBtn.textContent = '是，跳转';
                redirectBtn.style.cssText = `
                    background: #4a9eff; color: white; border: none; padding: 10px 20px;
                    border-radius: 4px; margin: 0 10px; cursor: pointer; font-size: 14px;
                `;
                redirectBtn.addEventListener('click', function() {
                    redirectToCorrectPage(redirectUrl);
                });

                // 创建继续按钮
                const stayBtn = document.createElement('button');
                stayBtn.textContent = '否，继续';
                stayBtn.style.cssText = `
                    background: #666; color: white; border: none; padding: 10px 20px;
                    border-radius: 4px; margin: 0 10px; cursor: pointer; font-size: 14px;
                `;
                stayBtn.addEventListener('click', function() {
                    stayOnCurrentPage();
                });

                buttonArea.appendChild(redirectBtn);
                buttonArea.appendChild(stayBtn);
            } else {
                // 创建确定按钮
                const okBtn = document.createElement('button');
                okBtn.textContent = '确定';
                okBtn.style.cssText = `
                    background: #4a9eff; color: white; border: none; padding: 10px 20px;
                    border-radius: 4px; cursor: pointer; font-size: 14px;
                `;
                okBtn.addEventListener('click', function() {
                    closeInspectionTypeDialog();
                });

                buttonArea.appendChild(okBtn);
            }

            // 组装对话框
            dialogContent.appendChild(titleArea);
            dialogContent.appendChild(messageArea);
            dialogContent.appendChild(buttonArea);
            dialogContainer.appendChild(dialogContent);

            // 添加到页面
            document.body.appendChild(dialogContainer);

            // 添加点击背景关闭功能
            dialogContainer.addEventListener('click', function(e) {
                if (e.target === dialogContainer) {
                    closeInspectionTypeDialog();
                }
            });
        }

        // 跳转到正确的检验页面
        function redirectToCorrectPage(url) {
            // 保存当前已填写的数据
            const currentData = {
                material_number: document.getElementById('material-number').value,
                supplier: document.getElementById('supplier').value,
                purchase_order: document.getElementById('purchase-order').value,
                receipt_date: document.getElementById('receipt-date').value,
                inspection_date: document.getElementById('inspection-date').value,
                total_quantity: document.getElementById('total-quantity').value
            };

            // 将数据存储到sessionStorage
            sessionStorage.setItem('inspection_form_data', JSON.stringify(currentData));

            // 跳转到目标页面
            window.location.href = url;
        }

        // 继续在当前页面
        function stayOnCurrentPage() {
            closeInspectionTypeDialog();
            // 可以选择清空检验类型或保持用户选择
            showAlert('您选择继续在抽样检验页面操作', 'info');
        }

        // 关闭检验类型对话框
        function closeInspectionTypeDialog() {
            const dialog = document.getElementById('inspection-type-dialog');
            if (dialog) {
                dialog.remove();
            }
        }

        // 恢复从其他页面跳转过来的表单数据
        function restoreFormDataFromSession() {
            const savedData = sessionStorage.getItem('inspection_form_data');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);

                    // 恢复表单数据
                    if (data.material_number) {
                        document.getElementById('material-number').value = data.material_number;
                        // 自动触发物料信息查询
                        setTimeout(() => {
                            fetchMaterialInfo();
                        }, 100);
                    }
                    if (data.supplier) document.getElementById('supplier').value = data.supplier;
                    if (data.purchase_order) document.getElementById('purchase-order').value = data.purchase_order;
                    if (data.receipt_date) document.getElementById('receipt-date').value = data.receipt_date;
                    if (data.inspection_date) document.getElementById('inspection-date').value = data.inspection_date;
                    if (data.total_quantity) document.getElementById('total-quantity').value = data.total_quantity;

                    // 清除sessionStorage中的数据
                    sessionStorage.removeItem('inspection_form_data');

                    showAlert('已恢复之前填写的数据', 'success');

                } catch (e) {
                    console.error('恢复表单数据失败:', e);
                    sessionStorage.removeItem('inspection_form_data');
                }
            }
        }

        // 为历史问题表格添加悬停提示功能
        function addHistoryTableTooltips() {
            const historyTable = document.getElementById('history-table');
            const tableCells = historyTable.querySelectorAll('tbody td');

            tableCells.forEach(cell => {
                // 跳过空状态的单元格
                if (cell.getAttribute('colspan')) return;

                let tooltip = null;
                let tooltipTimeout = null;

                cell.addEventListener('mouseenter', function() {
                    const fullText = this.textContent.trim();
                    const title = this.getAttribute('title');

                    if (fullText && title && fullText.length > 20) {
                        // 创建提示框
                        tooltip = document.createElement('div');
                        tooltip.className = 'history-tooltip';
                        tooltip.textContent = title;

                        // 添加到单元格中
                        this.style.position = 'relative';
                        this.appendChild(tooltip);

                        // 显示提示框
                        setTimeout(() => {
                            if (tooltip) {
                                tooltip.style.display = 'block';
                            }
                        }, 100);
                    }
                });

                cell.addEventListener('mouseleave', function() {
                    if (tooltip) {
                        tooltipTimeout = setTimeout(() => {
                            if (tooltip && tooltip.parentNode) {
                                tooltip.parentNode.removeChild(tooltip);
                                tooltip = null;
                            }
                        }, 200);
                    }
                });

                // 如果鼠标重新进入提示框，取消隐藏
                cell.addEventListener('mouseenter', function() {
                    if (tooltipTimeout) {
                        clearTimeout(tooltipTimeout);
                        tooltipTimeout = null;
                    }
                });
            });
        }

        // 加载附件信息
        function loadAttachments(materialNumber) {
            const attachmentsList = document.getElementById('attachments-list');
            attachmentsList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">加载中...</div>';

            // 调用物料管理的附件API
            fetch(`/material_management/api/material/attachments/${materialNumber}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.attachments.length > 0) {
                        attachmentsList.innerHTML = data.attachments.map(attachment => `
                            <div class="attachment-item">
                                <div class="attachment-header">
                                    <i class="fas ${getFileIcon(attachment.extension)} attachment-icon"></i>
                                    <span class="attachment-name" title="${attachment.filename}">${attachment.filename}</span>
                                </div>
                                <div class="attachment-footer">
                                    <span class="attachment-meta">${formatFileSize(attachment.size)} • ${formatDate(attachment.upload_time)}</span>
                                    <a href="javascript:void(0)" class="attachment-link" onclick="openAttachment('${attachment.filename}', '${attachment.path}', '${attachment.extension}')">打开</a>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        attachmentsList.innerHTML = '<div style="text-align: center; color: #999; padding: 20px;">暂无相关附件</div>';
                    }
                })
                .catch(error => {
                    console.error('加载附件失败:', error);
                    attachmentsList.innerHTML = '<div style="text-align: center; color: #f44336; padding: 20px;">加载失败</div>';
                });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }



        // 获取文件图标
        function getFileIcon(extension) {
            const iconMap = {
                'pdf': 'fa-file-pdf',
                'doc': 'fa-file-word',
                'docx': 'fa-file-word',
                'xls': 'fa-file-excel',
                'xlsx': 'fa-file-excel',
                'ppt': 'fa-file-powerpoint',
                'pptx': 'fa-file-powerpoint',
                'jpg': 'fa-file-image',
                'jpeg': 'fa-file-image',
                'png': 'fa-file-image',
                'gif': 'fa-file-image',
                'dwg': 'fa-file-alt',
                'dxf': 'fa-file-alt'
            };
            return iconMap[extension.toLowerCase()] || 'fa-file';
        }

        // 全局变量存储弹窗数据
        let modalHistoryData = [];
        let currentMaterialNumber = '';

        // 打开历史问题弹窗
        function openHistoryModal(materialNumber) {
            currentMaterialNumber = materialNumber;
            document.getElementById('history-modal').style.display = 'flex';
            document.body.style.overflow = 'hidden'; // 防止背景滚动

            // 更新弹窗标题
            document.querySelector('.modal-title').textContent = `物料 ${materialNumber} 的历史问题点`;

            // 加载数据
            loadModalHistoryData();
        }

        // 关闭历史问题弹窗
        function closeHistoryModal() {
            document.getElementById('history-modal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 加载弹窗历史数据
        function loadModalHistoryData() {
            const tbody = document.getElementById('modal-history-tbody');
            tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #999; padding: 40px;">加载中...</td></tr>';

            const periodSelect = document.getElementById('modal-period-select');
            const period = periodSelect.value;
            let url = `/incoming/api/history_issues/${currentMaterialNumber}?period=${period}`;

            if (period === 'custom') {
                const startDate = document.getElementById('modal-start-date').value;
                const endDate = document.getElementById('modal-end-date').value;
                if (startDate && endDate) {
                    url += `&start_date=${startDate}&end_date=${endDate}`;
                } else {
                    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #f44336; padding: 40px;">请选择开始和结束日期</td></tr>';
                    return;
                }
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.issues.length > 0) {
                        modalHistoryData = data.issues;
                        renderModalData(modalHistoryData);
                        updateModalStats(modalHistoryData.length);
                    } else {
                        modalHistoryData = [];
                        tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #999; padding: 40px;">暂无历史问题记录</td></tr>';
                        updateModalStats(0);
                    }
                })
                .catch(error => {
                    console.error('加载历史问题失败:', error);
                    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #f44336; padding: 40px;">加载失败</td></tr>';
                    updateModalStats(0);
                });
        }

        // 渲染弹窗数据
        function renderModalData(data) {
            const tbody = document.getElementById('modal-history-tbody');
            if (data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #999; padding: 40px;">暂无数据</td></tr>';
                return;
            }

            tbody.innerHTML = data.map((issue, index) => `
                <tr>
                    <td>${issue.inspection_date}</td>
                    <td><span style="padding: 2px 6px; background: #e3f2fd; border-radius: 3px; font-size: 11px;">${issue.issue_type}</span></td>
                    <td style="max-width: 300px; word-wrap: break-word;">${issue.issue_description}</td>
                    <td><span style="padding: 2px 6px; background: #f3e5f5; border-radius: 3px; font-size: 11px;">${issue.inspection_type || '抽样检验'}</span></td>
                    <td style="font-size: 11px;">${issue.supplier || '-'}</td>
                </tr>
            `).join('');
        }

        // 筛选弹窗数据
        function filterModalData() {
            const typeFilter = document.getElementById('modal-type-filter').value;
            const keywordFilter = document.getElementById('modal-keyword-filter').value.toLowerCase();

            let filteredData = modalHistoryData;

            // 按类型筛选
            if (typeFilter) {
                filteredData = filteredData.filter(item => item.issue_type === typeFilter);
            }

            // 按关键词筛选
            if (keywordFilter) {
                filteredData = filteredData.filter(item =>
                    item.issue_description.toLowerCase().includes(keywordFilter)
                );
            }

            renderModalData(filteredData);
            updateModalStats(filteredData.length, modalHistoryData.length);
        }

        // 更新统计信息
        function updateModalStats(filtered, total = null) {
            const statsElement = document.getElementById('modal-stats');
            if (total !== null && filtered !== total) {
                statsElement.textContent = `共找到 ${total} 条记录，筛选后显示 ${filtered} 条`;
            } else {
                statsElement.textContent = `共找到 ${filtered} 条记录`;
            }
        }

        // 添加问题点
        document.getElementById('add-issue-btn').addEventListener('click', function() {
            const issuesContainer = document.getElementById('issues-container');
            const issueCount = issuesContainer.querySelectorAll('.issue-row').length;

            if (issueCount >= 10) {
                showAlert('最多只能添加10个问题点', 'error');
                return;
            }

            const newIndex = issueCount + 1;
            const newRow = document.createElement('tr');
            newRow.className = 'issue-row';
            newRow.setAttribute('data-index', newIndex);

            newRow.innerHTML = `
                <td class="text-center">${newIndex}</td>
                <td>
                    <select class="issue-type" name="issue_type_${newIndex}" required>
                        <option value="">选择类型</option>
                        <option value="尺寸不良">尺寸不良</option>
                        <option value="外观不良">外观不良</option>
                        <option value="功能不良">功能不良</option>
                        <option value="包装不良">包装不良</option>
                        <option value="其他">其他</option>
                    </select>
                </td>
                <td>
                    <textarea class="issue-description" name="issue_desc_${newIndex}" rows="1" required placeholder="请详细描述问题"></textarea>
                </td>
                <td>
                    <div class="image-upload-container" data-row-index="${newIndex}">
                        <div class="image-add-btn" onclick="triggerImageUpload(this)">
                            <i class="fas fa-plus"></i>
                            <input type="file" accept="image/*" onchange="handleImageUpload(this)">
                        </div>
                    </div>
                </td>
            `;

            issuesContainer.appendChild(newRow);
        });

        // 删除问题点
        document.getElementById('remove-issue-btn').addEventListener('click', function() {
            const issuesContainer = document.getElementById('issues-container');
            const issueRows = issuesContainer.querySelectorAll('.issue-row');

            if (issueRows.length <= 1) {
                showAlert('至少保留一个问题点', 'error');
                return;
            }

            issuesContainer.removeChild(issueRows[issueRows.length - 1]);
        });

        // 表单提交
        document.getElementById('inspection-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 验证必填字段
            if (!validateForm()) {
                return;
            }

            const submitBtn = document.querySelector('.btn-submit');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';

            // 构建表单数据
            const formData = new FormData();

            // 基本信息
            formData.append('inspection_type', 'sampling');
            formData.append('material_number', materialNumberInput.value);
            formData.append('material_name', materialNameInput.value);
            formData.append('specification', specificationInput.value);
            formData.append('material_type', materialTypeInput.value);
            formData.append('color', colorInput.value);
            formData.append('supplier', document.getElementById('supplier').value);
            formData.append('purchase_order', document.getElementById('purchase-order').value);
            formData.append('receipt_date', document.getElementById('receipt-date').value);
            formData.append('inspection_date', document.getElementById('inspection-date').value);
            formData.append('total_quantity', document.getElementById('total-quantity').value);
            formData.append('sample_quantity', document.getElementById('sample-quantity').value);
            formData.append('defect_quantity', document.getElementById('defect-quantity').value);
            formData.append('inspector', document.getElementById('inspector').value);

            // 问题点信息
            const issueRows = document.querySelectorAll('.issue-row');
            const issueData = [];

            issueRows.forEach((row, index) => {
                const rowIndex = index + 1;
                const issueType = row.querySelector('.issue-type').value;
                const issueDesc = row.querySelector('.issue-description').value;

                if (issueType && issueDesc) {
                    const issueInfo = {
                        type: issueType,
                        description: issueDesc
                    };

                    issueData.push(issueInfo);

                    // 添加图片文件
                    const container = row.querySelector('.image-upload-container');
                    const hiddenInputs = container.querySelectorAll('input[type="file"][style*="display: none"]');
                    hiddenInputs.forEach(input => {
                        if (input.files && input.files[0]) {
                            formData.append(`issue_images_${rowIndex}[]`, input.files[0]);
                        }
                    });
                }
            });

            formData.append('issues', JSON.stringify(issueData));

            // 发送请求
            fetch('/incoming/api/add_inspection', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(async data => {
                if (data.success) {
                    let message = data.report_code ?
                        `抽样检验记录保存成功！报告编码：${data.report_code}` :
                        '抽样检验记录保存成功！';

                    // 保存关键尺寸测量数据并自动更新模板
                    if (dimensionMeasurement && data.inspection_id) {
                        dimensionMeasurement.setInspectionInfo(data.inspection_id, 'sampling');
                        const dimensionResult = await dimensionMeasurement.saveMeasurements(true); // 传入true表示自动更新模板

                        if (dimensionResult.success) {
                            message += '\n关键尺寸测量数据保存成功！';
                            if (dimensionResult.message.includes('模板已自动更新')) {
                                message += '\n标准尺寸模板已自动更新！';
                            }
                        } else if (dimensionResult.error && !dimensionResult.error.includes('没有尺寸测量数据')) {
                            message += '\n关键尺寸测量数据保存失败: ' + dimensionResult.error;
                        }
                    }

                    showAlert(message, 'success');
                    // 延迟跳转，让用户看到成功提示
                    setTimeout(() => {
                        window.location.href = '/sampling_inspection/';
                    }, 2500);
                } else {
                    showAlert('保存失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showAlert('提交失败: ' + error, 'error');
                console.error('提交错误:', error);
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save"></i> 提交抽样检验记录';
            });
        });

        // 表单验证函数
        function validateForm() {
            const requiredFields = [
                { id: 'material-number', name: '物料料号' },
                { id: 'material-name', name: '物料名称' },
                { id: 'supplier', name: '供应商' },
                { id: 'purchase-order', name: '采购单号' },
                { id: 'receipt-date', name: '来料日期' },
                { id: 'inspection-date', name: '检验日期' },
                { id: 'total-quantity', name: '来料数量' },
                { id: 'sample-quantity', name: '抽样数量' },
                { id: 'defect-quantity', name: '不良数量' },
                { id: 'inspector', name: '检验员' }
            ];

            for (const field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    showAlert(`请填写${field.name}`, 'error');
                    element.focus();
                    return false;
                }
            }

            // 验证数量逻辑
            const totalQty = parseInt(document.getElementById('total-quantity').value);
            const sampleQty = parseInt(document.getElementById('sample-quantity').value);
            const defectQty = parseInt(document.getElementById('defect-quantity').value);

            if (sampleQty > totalQty) {
                showAlert('抽样数量不能大于来料数量', 'error');
                return false;
            }

            if (defectQty > sampleQty) {
                showAlert('不良数量不能大于抽样数量', 'error');
                return false;
            }

            // 验证问题点
            const issueRows = document.querySelectorAll('.issue-row');
            for (let i = 0; i < issueRows.length; i++) {
                const row = issueRows[i];
                const issueType = row.querySelector('.issue-type').value;
                const issueDesc = row.querySelector('.issue-description').value.trim();

                if (!issueType || !issueDesc) {
                    showAlert(`请完整填写第${i + 1}个问题点的信息`, 'error');
                    return false;
                }
            }

            return true;
        }

        // 显示提示信息
        function showAlert(message, type) {
            // 移除现有的提示
            const existingAlert = document.querySelector('.floating-alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // 创建新的浮动提示
            const alert = document.createElement('div');
            alert.className = `floating-alert floating-alert-${type}`;
            alert.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                padding: 16px 24px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                z-index: 9999;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                min-width: 200px;
                text-align: center;
                animation: fadeInScale 0.3s ease-out;
                ${type === 'success' ?
                    'background-color: #4caf50; color: white;' :
                    'background-color: #f44336; color: white;'
                }
            `;
            alert.textContent = message;

            // 添加到页面body
            document.body.appendChild(alert);

            // 3.75秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.style.animation = 'fadeOutScale 0.3s ease-in';
                    setTimeout(() => {
                        if (alert.parentNode) {
                            alert.remove();
                        }
                    }, 300);
                }
            }, 3750);
        }
    });

    // 图片上传功能
    function triggerImageUpload(addBtn) {
        const fileInput = addBtn.querySelector('input[type="file"]');
        fileInput.click();
    }

    function handleImageUpload(fileInput) {
        const file = fileInput.files[0];
        if (!file) return;

        const container = fileInput.closest('.image-upload-container');
        const rowIndex = container.getAttribute('data-row-index');
        const currentImages = container.querySelectorAll('.image-preview-item').length;

        // 检查是否超过5张图片
        if (currentImages >= 5) {
            alert('每个问题点最多只能上传5张图片');
            fileInput.value = '';
            return;
        }

        // 检查文件大小（限制为5MB）
        if (file.size > 5 * 1024 * 1024) {
            alert('图片大小不能超过5MB');
            fileInput.value = '';
            return;
        }

        // 检查文件类型
        if (!file.type.startsWith('image/')) {
            alert('请选择图片文件');
            fileInput.value = '';
            return;
        }

        // 创建图片预览
        const reader = new FileReader();
        reader.onload = function(e) {
            createImagePreview(container, e.target.result, file, rowIndex);
        };
        reader.readAsDataURL(file);

        // 清空文件输入，允许重复选择同一文件
        fileInput.value = '';
    }

    function createImagePreview(container, imageSrc, file, rowIndex) {
        const addBtn = container.querySelector('.image-add-btn');

        // 创建图片预览项
        const previewItem = document.createElement('div');
        previewItem.className = 'image-preview-item';

        const img = document.createElement('img');
        img.src = imageSrc;
        img.alt = 'Preview';

        const removeBtn = document.createElement('button');
        removeBtn.className = 'image-remove-btn';
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.onclick = function() {
            removeImagePreview(previewItem, container);
        };

        previewItem.appendChild(img);
        previewItem.appendChild(removeBtn);

        // 创建隐藏的文件输入框来存储文件
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'file';
        hiddenInput.style.display = 'none';
        hiddenInput.name = `issue_images_${rowIndex}[]`;

        // 将文件数据转换为File对象并存储
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        hiddenInput.files = dataTransfer.files;

        previewItem.appendChild(hiddenInput);

        // 在添加按钮前插入预览项
        container.insertBefore(previewItem, addBtn);

        // 检查是否达到最大数量
        const currentImages = container.querySelectorAll('.image-preview-item').length;
        if (currentImages >= 5) {
            addBtn.style.display = 'none';
        }
    }

    function removeImagePreview(previewItem, container) {
        previewItem.remove();

        // 显示添加按钮
        const addBtn = container.querySelector('.image-add-btn');
        addBtn.style.display = 'flex';
    }
</script>
{% endblock %}
