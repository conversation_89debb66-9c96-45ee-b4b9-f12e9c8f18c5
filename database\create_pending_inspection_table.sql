-- 创建待检物料表
CREATE TABLE IF NOT EXISTS pending_inspections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL COMMENT '物料料号',
    material_name VARCHAR(200) COMMENT '物料名称',
    specification VARCHAR(500) COMMENT '规格型号',
    supplier_name VARCHAR(200) COMMENT '供应商名称',
    incoming_quantity DECIMAL(10,3) COMMENT '来料数量',
    unit VARCHAR(20) COMMENT '单位',
    inspection_type ENUM('sampling', 'full') NOT NULL COMMENT '检验类型：sampling-抽样检验，full-全部检验',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态：pending-待检，in_progress-检验中，completed-已完成，cancelled-已取消',
    batch_number VARCHAR(100) COMMENT '批次号',
    arrival_date DATE COMMENT '到货日期',
    planned_inspection_date DATE COMMENT '计划检验日期',
    inspector VARCHA<PERSON>(50) COMMENT '检验员',
    remarks TEXT COMMENT '备注',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(50) COMMENT '更新人',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    inspection_record_id INT COMMENT '关联的检验记录ID',
    
    INDEX idx_material_code (material_code),
    INDEX idx_inspection_type (inspection_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_arrival_date (arrival_date),
    
    FOREIGN KEY (inspection_record_id) REFERENCES sampling_inspection_records(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待检物料表';

-- 创建待检物料批次表（用于批量导入管理）
CREATE TABLE IF NOT EXISTS pending_inspection_batches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    batch_name VARCHAR(100) NOT NULL COMMENT '批次名称',
    inspection_type ENUM('sampling', 'full') NOT NULL COMMENT '检验类型',
    total_items INT DEFAULT 0 COMMENT '总物料数',
    pending_items INT DEFAULT 0 COMMENT '待检数量',
    in_progress_items INT DEFAULT 0 COMMENT '检验中数量',
    completed_items INT DEFAULT 0 COMMENT '已完成数量',
    created_by VARCHAR(50) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_inspection_type (inspection_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待检物料批次表';

-- 添加批次关联字段到待检物料表
ALTER TABLE pending_inspections 
ADD COLUMN batch_id INT COMMENT '批次ID',
ADD INDEX idx_batch_id (batch_id),
ADD FOREIGN KEY (batch_id) REFERENCES pending_inspection_batches(id) ON DELETE SET NULL;
