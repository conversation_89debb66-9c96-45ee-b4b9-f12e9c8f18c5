-- 批量导入待检表
CREATE TABLE IF NOT EXISTS batch_import_pending (
    id INT AUTO_INCREMENT PRIMARY KEY,
    material_code VARCHAR(100) NOT NULL COMMENT '物料料号',
    material_name VARCHAR(200) COMMENT '物料名称',
    specification VARCHAR(500) COMMENT '规格型号',
    supplier_name VARCHAR(200) COMMENT '供应商名称',
    incoming_quantity DECIMAL(10,2) COMMENT '来料数量',
    unit VARCHAR(50) COMMENT '单位',
    inspection_type ENUM('sampling', 'full') NOT NULL COMMENT '检验类型：sampling-抽样检验，full-全部检验',
    status ENUM('pending', 'processing', 'completed') DEFAULT 'pending' COMMENT '状态：pending-待检，processing-检验中，completed-已完成',
    created_by VARCHAR(100) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    notes TEXT COMMENT '备注',
    INDEX idx_material_code (material_code),
    INDEX idx_inspection_type (inspection_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批量导入待检表';

-- 批量导入历史表
CREATE TABLE IF NOT EXISTS batch_import_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    batch_id VARCHAR(50) NOT NULL COMMENT '批次ID',
    import_count INT NOT NULL COMMENT '导入数量',
    success_count INT DEFAULT 0 COMMENT '成功数量',
    failed_count INT DEFAULT 0 COMMENT '失败数量',
    inspection_type ENUM('sampling', 'full') NOT NULL COMMENT '检验类型',
    created_by VARCHAR(100) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    import_data JSON COMMENT '导入数据详情',
    error_details JSON COMMENT '错误详情',
    INDEX idx_batch_id (batch_id),
    INDEX idx_inspection_type (inspection_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批量导入历史表';
