{% extends "base.html" %}

{% block title %}待检清单 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}{% endblock %}

{% block extra_css %}
<style>
    .page-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e0e0e0;
    }

    .page-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
        font-weight: 600;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .btn-primary {
        background: #2196f3;
        color: white;
    }

    .btn-primary:hover {
        background: #1976d2;
    }

    .btn-success {
        background: #4caf50;
        color: white;
    }

    .btn-success:hover {
        background: #45a049;
    }

    .btn-warning {
        background: #ff9800;
        color: white;
    }

    .btn-warning:hover {
        background: #f57c00;
    }

    .btn-danger {
        background: #f44336;
        color: white;
    }

    .btn-danger:hover {
        background: #d32f2f;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
    }

    .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }

    .filter-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .filter-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        align-items: end;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
    }

    .filter-group label {
        font-size: 14px;
        color: #333;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .filter-group input,
    .filter-group select {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .filter-group input:focus,
    .filter-group select:focus {
        outline: none;
        border-color: #2196f3;
        box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
    }

    .data-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .data-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .data-stats {
        display: flex;
        gap: 20px;
        font-size: 14px;
        color: #666;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .stat-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .stat-pending {
        background: #fff3e0;
        color: #f57c00;
    }

    .stat-progress {
        background: #e3f2fd;
        color: #1976d2;
    }

    .stat-completed {
        background: #e8f5e8;
        color: #2e7d32;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }

    .data-table th,
    .data-table td {
        padding: 12px 8px;
        border: 1px solid #ddd;
        text-align: left;
        font-size: 14px;
    }

    .data-table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .data-table tr:nth-child(even) {
        background: #f9f9f9;
    }

    .data-table tr:hover {
        background: #f0f8ff;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        min-width: 60px;
        display: inline-block;
    }

    .status-pending {
        background: #fff3e0;
        color: #f57c00;
    }

    .status-progress {
        background: #e3f2fd;
        color: #1976d2;
    }

    .status-completed {
        background: #e8f5e8;
        color: #2e7d32;
    }

    .status-cancelled {
        background: #ffebee;
        color: #c62828;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        margin-top: 20px;
        padding: 20px 0;
    }

    .pagination button {
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        cursor: pointer;
        border-radius: 4px;
        font-size: 14px;
    }

    .pagination button:hover:not(:disabled) {
        background: #f0f8ff;
        border-color: #2196f3;
    }

    .pagination button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination .current-page {
        background: #2196f3;
        color: white;
        border-color: #2196f3;
    }

    .pagination-info {
        font-size: 14px;
        color: #666;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }

    .empty-state i {
        font-size: 4em;
        color: #ddd;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        margin: 20px 0 10px 0;
        color: #333;
    }

    .empty-state p {
        margin: 0;
        font-size: 14px;
    }

    .loading {
        text-align: center;
        padding: 40px;
        color: #666;
    }

    .spinner {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #2196f3;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        transform: translateX(0);
    }

    .toast.success {
        background: #4caf50;
    }

    .toast.error {
        background: #f44336;
    }

    .toast.warning {
        background: #ff9800;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .filter-row {
            grid-template-columns: 1fr;
        }
        
        .header-actions {
            flex-direction: column;
            gap: 5px;
        }
        
        .data-table {
            font-size: 12px;
        }
        
        .data-table th,
        .data-table td {
            padding: 8px 4px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="page-header">
        <h1>
            <i class="fas fa-list-check"></i> 
            待检清单 - {{ '抽样检验' if inspection_type == 'sampling' else '全部检验' }}
        </h1>
        <div class="header-actions">
            <a href="{{ url_for('incoming_inspection.batch_import_' + inspection_type) }}" class="btn btn-success">
                <i class="fas fa-upload"></i> 批量导入
            </a>
            <a href="{{ url_for('incoming_inspection.new_' + inspection_type + '_inspection') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> 新增检验
            </a>
        </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
        <div class="filter-row">
            <div class="filter-group">
                <label for="status-filter">状态</label>
                <select id="status-filter">
                    <option value="">全部状态</option>
                    <option value="pending">待检</option>
                    <option value="in_progress">检验中</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="material-code-filter">物料料号</label>
                <input type="text" id="material-code-filter" placeholder="输入物料料号">
            </div>
            <div class="filter-group">
                <label for="supplier-filter">供应商</label>
                <input type="text" id="supplier-filter" placeholder="输入供应商名称">
            </div>
            <div class="filter-group">
                <label for="date-from">到货日期从</label>
                <input type="date" id="date-from">
            </div>
            <div class="filter-group">
                <label for="date-to">到货日期至</label>
                <input type="date" id="date-to">
            </div>
            <div class="filter-group">
                <label>&nbsp;</label>
                <div style="display: flex; gap: 10px;">
                    <button type="button" class="btn btn-primary" onclick="applyFilters()">
                        <i class="fas fa-search"></i> 查询
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据区域 -->
    <div class="data-section">
        <div class="data-header">
            <div class="data-stats" id="data-stats">
                <div class="stat-item">
                    <span>总计:</span>
                    <span class="stat-badge" id="total-count">0</span>
                </div>
                <div class="stat-item">
                    <span>待检:</span>
                    <span class="stat-badge stat-pending" id="pending-count">0</span>
                </div>
                <div class="stat-item">
                    <span>检验中:</span>
                    <span class="stat-badge stat-progress" id="progress-count">0</span>
                </div>
                <div class="stat-item">
                    <span>已完成:</span>
                    <span class="stat-badge stat-completed" id="completed-count">0</span>
                </div>
            </div>
            <div>
                <button type="button" class="btn btn-secondary btn-sm" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在加载数据...</p>
        </div>

        <!-- 数据表格 -->
        <div id="data-container" style="display: none;">
            <table class="data-table">
                <thead>
                    <tr>
                        <th width="100">物料料号</th>
                        <th width="150">物料名称</th>
                        <th width="120">规格型号</th>
                        <th width="120">供应商</th>
                        <th width="80">来料数量</th>
                        <th width="60">单位</th>
                        <th width="100">批次号</th>
                        <th width="100">到货日期</th>
                        <th width="80">状态</th>
                        <th width="80">检验员</th>
                        <th width="120">创建时间</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody id="data-tbody">
                    <!-- 数据将在这里动态生成 -->
                </tbody>
            </table>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="empty-state" style="display: none;">
            <i class="fas fa-inbox"></i>
            <h3>暂无待检物料</h3>
            <p>点击"批量导入"开始添加待检物料</p>
        </div>

        <!-- 分页 -->
        <div class="pagination" id="pagination" style="display: none;">
            <button type="button" id="prev-page" onclick="changePage(currentPage - 1)">
                <i class="fas fa-chevron-left"></i> 上一页
            </button>
            <div class="pagination-info" id="pagination-info">
                第 1 页，共 1 页
            </div>
            <button type="button" id="next-page" onclick="changePage(currentPage + 1)">
                下一页 <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- Toast 提示 -->
<div id="toast" class="toast"></div>
{% endblock %}

{% block extra_js %}
<script>
    const inspectionType = '{{ inspection_type }}';
    let currentPage = 1;
    let totalPages = 1;
    let currentFilters = {};

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadData();
    });

    async function loadData(page = 1) {
        showLoading();
        
        try {
            const params = new URLSearchParams({
                type: inspectionType,
                page: page,
                per_page: 20,
                ...currentFilters
            });

            const response = await fetch(`/pending_inspection/api/pending_inspections?${params}`);
            const data = await response.json();

            if (data.success) {
                renderData(data.data);
                updateStats(data.data);
                updatePagination(data.data);
            } else {
                showToast('加载数据失败: ' + data.error, 'error');
                showEmptyState();
            }
        } catch (error) {
            console.error('加载数据失败:', error);
            showToast('加载数据失败，请重试', 'error');
            showEmptyState();
        } finally {
            hideLoading();
        }
    }

    function renderData(data) {
        const tbody = document.getElementById('data-tbody');
        
        if (data.items.length === 0) {
            showEmptyState();
            return;
        }

        let html = '';
        data.items.forEach(item => {
            html += `
                <tr>
                    <td>${item.material_code}</td>
                    <td>${item.material_name || ''}</td>
                    <td>${item.specification || ''}</td>
                    <td>${item.supplier_name || ''}</td>
                    <td>${item.incoming_quantity || ''}</td>
                    <td>${item.unit || ''}</td>
                    <td>${item.batch_number || ''}</td>
                    <td>${item.arrival_date || ''}</td>
                    <td>${getStatusBadge(item.status)}</td>
                    <td>${item.inspector || ''}</td>
                    <td>${item.created_at}</td>
                    <td>${getActionButtons(item)}</td>
                </tr>
            `;
        });

        tbody.innerHTML = html;
        document.getElementById('data-container').style.display = 'block';
        document.getElementById('empty-state').style.display = 'none';
    }

    function getStatusBadge(status) {
        const statusMap = {
            'pending': { text: '待检', class: 'status-pending' },
            'in_progress': { text: '检验中', class: 'status-progress' },
            'completed': { text: '已完成', class: 'status-completed' },
            'cancelled': { text: '已取消', class: 'status-cancelled' }
        };
        
        const statusInfo = statusMap[status] || { text: status, class: 'status-pending' };
        return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
    }

    function getActionButtons(item) {
        let buttons = '';
        
        if (item.status === 'pending') {
            buttons += `
                <button type="button" class="btn btn-success btn-sm" onclick="startInspection(${item.id})">
                    <i class="fas fa-play"></i> 开始检验
                </button>
                <button type="button" class="btn btn-warning btn-sm" onclick="editItem(${item.id})">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="deleteItem(${item.id})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            `;
        } else if (item.status === 'in_progress') {
            buttons += `
                <a href="/incoming_inspection/${inspectionType}_inspection/${item.inspection_record_id}/edit" class="btn btn-primary btn-sm">
                    <i class="fas fa-edit"></i> 继续检验
                </a>
            `;
        } else if (item.status === 'completed') {
            buttons += `
                <a href="/incoming_inspection/${inspectionType}_inspection/${item.inspection_record_id}/view" class="btn btn-secondary btn-sm">
                    <i class="fas fa-eye"></i> 查看记录
                </a>
            `;
        }
        
        return `<div class="action-buttons">${buttons}</div>`;
    }

    function updateStats(data) {
        // 计算各状态数量
        const stats = {
            total: data.total,
            pending: 0,
            in_progress: 0,
            completed: 0
        };

        data.items.forEach(item => {
            if (stats[item.status] !== undefined) {
                stats[item.status]++;
            }
        });

        document.getElementById('total-count').textContent = stats.total;
        document.getElementById('pending-count').textContent = stats.pending;
        document.getElementById('progress-count').textContent = stats.in_progress;
        document.getElementById('completed-count').textContent = stats.completed;
    }

    function updatePagination(data) {
        currentPage = data.page;
        totalPages = data.pages;

        if (totalPages <= 1) {
            document.getElementById('pagination').style.display = 'none';
            return;
        }

        document.getElementById('pagination').style.display = 'flex';
        document.getElementById('pagination-info').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
        document.getElementById('prev-page').disabled = currentPage <= 1;
        document.getElementById('next-page').disabled = currentPage >= totalPages;
    }

    function changePage(page) {
        if (page >= 1 && page <= totalPages) {
            loadData(page);
        }
    }

    function applyFilters() {
        currentFilters = {
            status: document.getElementById('status-filter').value,
            material_code: document.getElementById('material-code-filter').value,
            supplier: document.getElementById('supplier-filter').value,
            date_from: document.getElementById('date-from').value,
            date_to: document.getElementById('date-to').value
        };

        // 移除空值
        Object.keys(currentFilters).forEach(key => {
            if (!currentFilters[key]) {
                delete currentFilters[key];
            }
        });

        loadData(1);
    }

    function resetFilters() {
        document.getElementById('status-filter').value = '';
        document.getElementById('material-code-filter').value = '';
        document.getElementById('supplier-filter').value = '';
        document.getElementById('date-from').value = '';
        document.getElementById('date-to').value = '';
        
        currentFilters = {};
        loadData(1);
    }

    function refreshData() {
        loadData(currentPage);
    }

    async function startInspection(itemId) {
        if (!confirm('确定要开始检验吗？这将创建一个新的检验记录。')) {
            return;
        }

        try {
            const response = await fetch(`/pending_inspection/api/pending_inspections/${itemId}/start_inspection`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                showToast('检验记录创建成功', 'success');
                
                // 跳转到检验页面
                setTimeout(() => {
                    window.location.href = `/incoming_inspection/${data.data.inspection_type}_inspection/${data.data.inspection_record_id}/edit`;
                }, 1000);
            } else {
                showToast('创建检验记录失败: ' + data.error, 'error');
            }
        } catch (error) {
            console.error('开始检验失败:', error);
            showToast('开始检验失败，请重试', 'error');
        }
    }

    function editItem(itemId) {
        // 这里可以打开编辑对话框或跳转到编辑页面
        showToast('编辑功能开发中', 'warning');
    }

    async function deleteItem(itemId) {
        if (!confirm('确定要删除这个待检物料吗？')) {
            return;
        }

        try {
            const response = await fetch(`/pending_inspection/api/pending_inspections/${itemId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                showToast('删除成功', 'success');
                loadData(currentPage);
            } else {
                showToast('删除失败: ' + data.error, 'error');
            }
        } catch (error) {
            console.error('删除失败:', error);
            showToast('删除失败，请重试', 'error');
        }
    }

    function showLoading() {
        document.getElementById('loading').style.display = 'block';
        document.getElementById('data-container').style.display = 'none';
        document.getElementById('empty-state').style.display = 'none';
    }

    function hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    function showEmptyState() {
        document.getElementById('data-container').style.display = 'none';
        document.getElementById('empty-state').style.display = 'block';
        document.getElementById('pagination').style.display = 'none';
    }

    function showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.className = `toast ${type} show`;
        
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }
</script>
{% endblock %}
